2025-09-01 21:13:21.470 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:13:21.490 -03:00 [INF] Hosting environment: Production
2025-09-01 21:13:21.490 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:13:21.777 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:13:22.584 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:13:23.021 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:13:23.027 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:13:23.050 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:13:23.051 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:13:23.369 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:13:23.370 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:13:23.371 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:13:23.371 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:13:23.371 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:13:23.371 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:13:23.371 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:13:23.371 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:13:23.371 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:13:23.373 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:13:23.483 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:14:05.346 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:14:05.347 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIANU, Symbol=R_10, Stake=1.00
2025-09-01 21:14:05.354 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:14:10.555 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:14:10.555 -03:00 [INF] [DEBUG] Saindo: Barrier1 obrigatória mas vazia. IsBarrier1Visible=True, Barrier1Value=''
2025-09-01 21:14:10.555 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-09-01 21:14:10.555 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:15:07.992 -03:00 [INF] Application is shutting down...
2025-09-01 21:20:12.617 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:20:12.638 -03:00 [INF] Hosting environment: Production
2025-09-01 21:20:12.639 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:20:12.853 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:20:13.309 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:20:13.704 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:20:13.707 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:20:13.730 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:20:13.730 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:20:13.971 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:20:13.972 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:20:13.973 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:20:13.973 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:20:13.973 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:20:13.973 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:20:13.973 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:20:13.973 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:20:13.973 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:20:13.975 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:20:14.000 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:20:14.000 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:20:14.001 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:20:14.001 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:20:14.001 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:20:14.001 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:20:14.001 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:20:14.001 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:20:14.001 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:20:14.001 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:20:58.074 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:20:58.075 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 21:20:58.082 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:21:00.862 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:00.862 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 21:21:11.086 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:11.086 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:21:11.326 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:11.326 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:21:11.541 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:11.541 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.5.35'
2025-09-01 21:21:11.980 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:11.980 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:21:12.125 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:12.125 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:21:14.223 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:14.224 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:21:16.754 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:16.754 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:21:17.106 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:17.106 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:21:17.254 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:17.254 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.4.35'
2025-09-01 21:21:17.762 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:17.762 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.40.35'
2025-09-01 21:21:18.859 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:21:18.860 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 21:23:34.214 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:34.214 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: ''
2025-09-01 21:23:34.915 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:34.915 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:35.189 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:35.189 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:23:35.527 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:35.527 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.35'
2025-09-01 21:23:37.801 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:37.801 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.3'
2025-09-01 21:23:38.306 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:38.306 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.'
2025-09-01 21:23:38.337 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:38.337 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:38.367 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:38.367 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:23:38.401 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:38.401 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:23:38.434 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:38.434 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:23:40.797 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:40.797 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:41.598 -03:00 [ERR] Erro ao calcular proposta
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 397
   at Excalibur.ViewModels.MainViewModel.CalculateProposalAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1194
2025-09-01 21:23:44.383 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:44.383 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:44.702 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:44.702 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.50
2025-09-01 21:23:47.811 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:47.811 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:48.319 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:48.319 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:48.412 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:48.412 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:23:50.615 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:50.615 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:51.149 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:51.149 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:52.081 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:52.082 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=4.35
2025-09-01 21:23:55.208 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:55.208 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:57.250 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:57.250 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:23:57.752 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:57.752 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.50
2025-09-01 21:23:57.781 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:57.781 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:23:57.815 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:57.815 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:23:57.846 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:57.846 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:23:57.879 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:57.879 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:23:57.911 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:23:57.911 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:29:20.218 -03:00 [INF] Application is shutting down...
2025-09-01 21:30:13.696 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:30:13.736 -03:00 [INF] Hosting environment: Production
2025-09-01 21:30:13.736 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:30:13.947 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:30:14.473 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:30:14.866 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:30:14.869 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:30:14.971 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:30:14.971 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:30:15.144 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:30:15.152 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:30:15.153 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:30:15.153 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:30:15.153 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:30:15.153 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:30:15.153 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:30:15.153 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:30:15.153 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:30:15.159 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:30:15.239 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:30:15.239 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:30:15.240 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:30:15.240 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:30:15.240 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:30:15.240 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:30:15.240 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:30:15.240 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:30:15.240 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:30:15.242 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:30:34.004 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:30:34.005 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 21:30:34.012 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:30:36.821 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:30:36.821 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 21:30:43.184 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:30:43.184 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 21:30:51.719 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:30:51.719 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-01 21:31:00.245 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:31:00.245 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 21:31:10.207 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:31:10.207 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 21:31:19.520 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 21:31:19.523 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:31:19.523
2025-09-01 21:31:19.523 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 21:31:19.720 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass266_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1415
2025-09-01 21:31:19.730 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass266_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1415
2025-09-01 21:31:19.731 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass266_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1415
2025-09-01 21:31:19.738 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass266_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1415
2025-09-01 21:31:19.745 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass266_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1415
2025-09-01 21:31:19.746 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 222.4104ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 21:31:19.746 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 21:31:19.746 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 21:31:20.300 -03:00 [INF] [DEBUG] Fast Martingale DISABLED - clearing hot pool
2025-09-01 21:36:32.040 -03:00 [INF] Application is shutting down...
2025-09-01 21:37:20.060 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:37:20.089 -03:00 [INF] Hosting environment: Production
2025-09-01 21:37:20.089 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:37:20.465 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:37:20.977 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:37:21.467 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:37:21.470 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:37:21.758 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:37:21.759 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:37:21.924 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:37:21.924 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:37:21.925 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:37:21.925 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:37:21.925 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:37:21.925 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:37:21.925 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:37:21.925 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:37:21.925 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:37:21.927 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:37:22.014 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:37:56.846 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:37:56.847 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:37:56.855 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:37:59.226 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:37:59.226 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:38:03.138 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:03.138 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:03.546 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:03.546 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:38:06.393 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:06.393 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:06.703 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:06.703 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:06.858 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:06.858 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:38:08.987 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:08.987 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=4.3
2025-09-01 21:38:09.149 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:09.150 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:38:09.328 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:09.329 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 21:38:09.581 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:09.582 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 21:38:10.043 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:10.044 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:10.294 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:10.294 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=35.00
2025-09-01 21:38:10.800 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:10.800 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=35.00
2025-09-01 21:38:11.155 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:11.155 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=35.00
2025-09-01 21:38:11.375 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:11.375 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:38:13.590 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:13.591 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:13.938 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:13.939 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:14.134 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:14.134 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:38:15.756 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:15.756 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:16.455 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:16.455 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=4.35
2025-09-01 21:38:19.276 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:19.276 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:19.484 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:19.484 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:38:19.611 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:19.611 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=4.35
2025-09-01 21:38:20.450 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:20.450 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:21.214 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:21.214 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:21.544 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:21.544 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:38:21.686 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:21.686 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.4.35'
2025-09-01 21:38:30.247 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:30.247 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:38:32.583 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:32.583 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:38:32.738 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:32.738 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:38:35.469 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:35.469 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:35.776 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:35.776 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:38:42.988 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:42.989 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..3'
2025-09-01 21:38:43.198 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:43.198 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..'
2025-09-01 21:38:43.359 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:43.381 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:43.587 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:43.587 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:38:44.573 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:44.573 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=35.00
2025-09-01 21:38:45.021 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:45.021 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=35.00
2025-09-01 21:38:45.193 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:45.193 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:38:45.379 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:45.379 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=300.00
2025-09-01 21:38:45.638 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:45.638 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:48.610 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:48.610 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:48.848 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:48.848 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:38:49.122 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:38:49.122 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=3.35
2025-09-01 21:39:06.901 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:39:06.901 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=3.35
2025-09-01 21:39:06.926 -03:00 [INF] Application is shutting down...
2025-09-01 21:44:37.192 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:44:37.224 -03:00 [INF] Hosting environment: Production
2025-09-01 21:44:37.225 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:44:37.544 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:44:38.327 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:44:38.796 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:44:38.799 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:44:38.820 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:44:38.821 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:44:39.361 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:44:39.362 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:44:39.362 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:44:39.362 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:44:39.362 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:44:39.362 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:44:39.362 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:44:39.362 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:44:39.363 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:44:39.365 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:44:39.482 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:44:39.482 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:44:39.483 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:44:39.483 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:44:39.483 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:44:39.483 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:44:39.483 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:44:39.483 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:44:39.483 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:44:39.483 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:44:53.913 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:44:53.914 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:44:53.921 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:44:56.228 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:44:56.228 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:44:58.237 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:44:58.237 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:44:58.566 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:44:58.566 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:44:58.982 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:44:58.982 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.35'
2025-09-01 21:44:59.352 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:44:59.352 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.35.35'
2025-09-01 21:45:02.149 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.149 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.35.'
2025-09-01 21:45:02.653 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.653 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:45:02.686 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.687 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:02.717 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.718 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:02.749 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.749 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:02.781 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.781 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:02.813 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.813 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:02.846 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.846 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:02.878 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:02.878 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:05.531 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:05.531 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:45:05.730 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:05.730 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:05.887 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:05.887 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=50.00
2025-09-01 21:45:06.031 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:06.031 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:06.544 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:06.544 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=5000.00
2025-09-01 21:45:06.727 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:06.727 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:06.878 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:06.878 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:45:08.117 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:08.117 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:45:08.371 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:08.371 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:08.504 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:08.504 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:45:08.657 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:08.657 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.35.35'
2025-09-01 21:45:11.392 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:11.392 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:45:11.620 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:11.620 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:45:11.729 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:11.729 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.35'
2025-09-01 21:45:11.834 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:11.834 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.35.35'
2025-09-01 21:45:12.415 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:12.416 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.35'
2025-09-01 21:45:12.559 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:12.559 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:45:12.820 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:12.820 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.4.35'
2025-09-01 21:45:13.072 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:45:13.072 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.40.35'
2025-09-01 21:47:21.132 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:21.132 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.40.35'
2025-09-01 21:47:21.250 -03:00 [INF] Application is shutting down...
2025-09-01 21:47:29.877 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:47:29.904 -03:00 [INF] Hosting environment: Production
2025-09-01 21:47:29.905 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:47:30.266 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:47:30.995 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:47:31.456 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:47:31.460 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:47:31.626 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:47:31.626 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:47:31.974 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:47:31.975 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:47:31.975 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:47:31.975 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:47:31.975 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:47:31.975 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:47:31.975 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:47:31.975 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:47:31.975 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:47:31.978 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:47:32.067 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:47:42.778 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:42.778 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIANU, Symbol=R_10, Stake=1.00
2025-09-01 21:47:42.785 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:47:46.898 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:46.898 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIANU, Symbol=R_10, Stake=1.00
2025-09-01 21:47:47.122 -03:00 [ERR] Erro ao calcular proposta
System.Exception: Number of ticks must be between 5 and 10.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 397
   at Excalibur.ViewModels.MainViewModel.CalculateProposalAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1194
2025-09-01 21:47:49.031 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:49.031 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIANU, Symbol=R_10, Stake=2.00
2025-09-01 21:47:49.222 -03:00 [ERR] Erro ao calcular proposta
System.Exception: Number of ticks must be between 5 and 10.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 397
   at Excalibur.ViewModels.MainViewModel.CalculateProposalAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1194
2025-09-01 21:47:51.540 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:51.540 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIANU, Symbol=R_10, Stake=0.35
2025-09-01 21:47:51.761 -03:00 [ERR] Erro ao calcular proposta
System.Exception: Number of ticks must be between 5 and 10.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 397
   at Excalibur.ViewModels.MainViewModel.CalculateProposalAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1194
2025-09-01 21:47:51.808 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:51.808 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..35'
2025-09-01 21:47:52.842 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:52.842 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.35'
2025-09-01 21:47:53.017 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:53.017 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.34.35'
2025-09-01 21:47:55.297 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:55.297 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.35'
2025-09-01 21:47:56.429 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:47:56.429 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.39.35'
2025-09-01 21:48:05.050 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:48:05.050 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.39.35'
2025-09-01 21:48:05.102 -03:00 [INF] Application is shutting down...
2025-09-01 21:51:14.887 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:51:15.735 -03:00 [INF] Hosting environment: Production
2025-09-01 21:51:15.735 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:51:16.002 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:51:16.679 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:51:17.123 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:51:17.127 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:51:17.160 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:51:17.161 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:51:17.622 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:51:17.622 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:51:17.623 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:51:17.623 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:51:17.623 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:51:17.623 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:51:17.623 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:51:17.623 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:51:17.623 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:51:17.625 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:51:17.733 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:51:17.734 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:51:38.361 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:38.362 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 21:51:38.370 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:51:41.009 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:41.009 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 21:51:44.024 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:44.024 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.00'
2025-09-01 21:51:44.466 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:44.467 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..00'
2025-09-01 21:51:44.702 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:44.702 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.00'
2025-09-01 21:51:45.062 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:45.062 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.35.00'
2025-09-01 21:51:49.515 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:49.515 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.35=.00'
2025-09-01 21:51:49.716 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:49.716 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.35.00'
2025-09-01 21:51:50.046 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:50.046 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.3.00'
2025-09-01 21:51:50.372 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:50.372 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..00'
2025-09-01 21:51:50.583 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:50.583 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.4.00'
2025-09-01 21:51:51.060 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:51.060 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..00'
2025-09-01 21:51:51.180 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:51.180 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.00'
2025-09-01 21:51:51.407 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:51.407 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0,.00'
2025-09-01 21:51:51.497 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:51.497 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=4.00
2025-09-01 21:51:51.871 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:51.871 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=40.00
2025-09-01 21:51:55.344 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:55.344 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.00'
2025-09-01 21:51:55.628 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:55.628 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0,.00'
2025-09-01 21:51:55.764 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:55.764 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=4.00
2025-09-01 21:51:57.780 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:57.780 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0,.00'
2025-09-01 21:51:57.931 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:57.931 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.00'
2025-09-01 21:51:58.240 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:58.240 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0..00'
2025-09-01 21:51:58.418 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:58.418 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.4.00'
2025-09-01 21:51:59.880 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:51:59.880 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.40.00'
2025-09-01 21:52:15.860 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:52:15.860 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.40.00'
2025-09-01 21:52:15.908 -03:00 [INF] Application is shutting down...
2025-09-01 21:54:24.262 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:54:24.284 -03:00 [INF] Hosting environment: Production
2025-09-01 21:54:24.284 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:54:24.510 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:54:25.142 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:54:25.531 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:54:25.534 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:54:25.584 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:54:25.584 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:54:26.052 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:54:26.053 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:54:26.053 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:54:26.053 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:54:26.053 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:54:26.053 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:54:26.053 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:54:26.053 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:54:26.053 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:54:26.056 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:54:26.151 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:54:26.152 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:54:40.017 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:40.018 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:54:40.025 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:54:42.801 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:42.801 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:54:45.659 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:45.659 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 21:54:45.929 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:45.930 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 21:54:46.075 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:46.075 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-01 21:54:46.269 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:46.269 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:54:46.821 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:46.821 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-01 21:54:46.930 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:46.930 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:54:47.058 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:47.058 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 21:54:47.356 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:47.356 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0,'
2025-09-01 21:54:47.504 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:47.504 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=3
2025-09-01 21:54:47.744 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:48.205 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=35
2025-09-01 21:54:51.877 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:51.877 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=3
2025-09-01 21:54:52.096 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:52.096 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:54:52.256 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:52.256 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 21:54:52.621 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:52.621 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 21:54:52.763 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:52.763 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-01 21:54:52.884 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:52.885 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:54:56.270 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:56.270 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-01 21:54:56.467 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:56.467 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:54:56.620 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:56.620 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 21:54:58.627 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:58.627 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0,'
2025-09-01 21:54:58.754 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:58.754 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=3
2025-09-01 21:54:58.937 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:54:58.937 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:55:06.580 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:55:06.580 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=3
2025-09-01 21:55:06.765 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:55:07.336 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:55:07.343 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:55:07.343 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 21:55:08.039 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:55:08.039 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 21:55:08.179 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:55:08.976 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-01 21:55:08.981 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:55:08.981 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:56:58.256 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:56:58.256 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-01 21:56:58.293 -03:00 [INF] Application is shutting down...
2025-09-01 21:57:04.993 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 21:57:05.022 -03:00 [INF] Hosting environment: Production
2025-09-01 21:57:05.023 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 21:57:05.410 -03:00 [INF] Conectando à API Deriv...
2025-09-01 21:57:05.804 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 21:57:06.395 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:57:06.399 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:57:06.654 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:57:06.656 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 21:57:06.657 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 21:57:06.658 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 21:57:07.017 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 21:58:26.702 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:26.703 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:58:26.710 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 21:58:28.940 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:28.940 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 21:58:32.180 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:32.180 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 21:58:32.527 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:32.527 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 21:58:32.668 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:32.668 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.4
2025-09-01 21:58:33.621 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:33.621 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 21:58:35.021 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:35.021 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.4
2025-09-01 21:58:35.194 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:35.194 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-01 21:58:35.316 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:35.344 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 21:58:35.676 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:35.676 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 21:58:35.816 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:35.816 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.4
2025-09-01 21:58:36.496 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 21:58:36.496 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:01:43.530 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:01:43.530 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:01:43.556 -03:00 [INF] Application is shutting down...
2025-09-01 22:03:27.240 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 22:03:27.272 -03:00 [INF] Hosting environment: Production
2025-09-01 22:03:27.272 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 22:03:27.549 -03:00 [INF] Conectando à API Deriv...
2025-09-01 22:03:28.163 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 22:03:28.552 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:03:28.555 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:03:28.580 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:03:28.581 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:03:29.018 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:03:29.018 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:03:29.019 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:03:29.019 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:03:29.019 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:03:29.019 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:03:29.019 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:03:29.019 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:03:29.019 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:03:29.021 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:03:29.086 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:03:29.087 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:03:47.210 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:03:47.211 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIANU, Symbol=R_10, Stake=1.00
2025-09-01 22:03:47.218 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:03:50.703 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:03:50.703 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=DIGITEVEN, Symbol=R_10, Stake=1.00
2025-09-01 22:03:50.704 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-09-01 22:03:50.704 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:03:57.745 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:03:57.745 -03:00 [INF] [DEBUG] Saindo: Barrier1 obrigatória mas vazia. IsBarrier1Visible=True, Barrier1Value=''
2025-09-01 22:03:57.746 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-09-01 22:03:57.746 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:04:33.824 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:04:33.824 -03:00 [INF] [DEBUG] Saindo: Barrier1 obrigatória mas vazia. IsBarrier1Visible=True, Barrier1Value=''
2025-09-01 22:04:33.824 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-09-01 22:04:33.825 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:04:37.770 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:04:37.771 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=1.00
2025-09-01 22:04:37.771 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-09-01 22:04:37.771 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:04:43.997 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:04:43.997 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 22:04:44.226 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:04:44.226 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 22:04:44.359 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:04:44.359 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=0.4
2025-09-01 22:04:45.861 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:04:45.861 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=0.40
2025-09-01 22:04:47.676 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:04:47.676 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=0.40
2025-09-01 22:04:52.616 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 22:04:52.617 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:04:52.617
2025-09-01 22:04:52.617 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:04:52.798 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:52.804 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:52.807 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:52.811 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:52.833 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:52.834 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 216.6496ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:04:52.834 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:04:52.834 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 22:04:57.831 -03:00 [INF] [DEBUG] Contrato comprado: 292947992728, subscrevendo para atualizações
2025-09-01 22:04:57.831 -03:00 [INF] Compra executada com sucesso. ContractId: 292947992728, TransactionId: 583517908708
2025-09-01 22:04:57.832 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:04:57.832
2025-09-01 22:04:57.832 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:04:57.832
2025-09-01 22:04:57.832 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:04:58.039 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:58.039 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:58.040 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:58.045 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:58.047 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:04:58.078 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 245.3799ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:04:58.078 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:04:58.078 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 245.5824ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:04:58.078 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:04:58.078 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:04:58.078 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=0.40
2025-09-01 22:05:10.996 -03:00 [INF] [TIMING] FALLBACK - Contrato 292947992728 detectado como finalizado às 22:05:10.996
2025-09-01 22:05:10.996 -03:00 [INF] Contract 292947992728 finished: Profit=0.36, Win=True
2025-09-01 22:05:10.996 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:05:10.996
2025-09-01 22:05:10.996 -03:00 [INF] [TIMING] Contract WIN at 22:05:10.996 - calling OnContractWin
2025-09-01 22:05:10.997 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:05:10.997 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=0.40
2025-09-01 22:05:10.998 -03:00 [INF] Profit Table updated for contract 292947992728: Profit=0.36, ExitPrice=6017.287, ExitTime=22:05:10
2025-09-01 22:05:10.999 -03:00 [INF] [TIMING] ContractResult event concluído às 22:05:10.998 (duração: 2.381ms)
2025-09-01 22:05:34.316 -03:00 [INF] [DEBUG] Contrato comprado: 292948023128, subscrevendo para atualizações
2025-09-01 22:05:34.316 -03:00 [INF] Compra executada com sucesso. ContractId: 292948023128, TransactionId: 583517966028
2025-09-01 22:05:34.316 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:05:34.316
2025-09-01 22:05:34.316 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:05:34.316
2025-09-01 22:05:34.316 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:05:34.540 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:05:34.540 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:05:34.540 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:05:34.541 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:05:34.541 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:05:34.541 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 225.1158ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:05:34.541 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:05:34.541 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 225.2016ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:05:34.541 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:05:34.541 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:05:34.541 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=0.40
2025-09-01 22:05:47.026 -03:00 [INF] [TIMING] FALLBACK - Contrato 292948023128 detectado como finalizado às 22:05:47.026
2025-09-01 22:05:47.026 -03:00 [INF] Contract 292948023128 finished: Profit=0.36, Win=True
2025-09-01 22:05:47.026 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:05:47.026
2025-09-01 22:05:47.026 -03:00 [INF] [TIMING] Contract WIN at 22:05:47.026 - calling OnContractWin
2025-09-01 22:05:47.026 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:05:47.026 -03:00 [INF] Profit Table updated for contract 292948023128: Profit=0.36, ExitPrice=6016.919, ExitTime=22:05:47
2025-09-01 22:05:47.026 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=0.40
2025-09-01 22:05:47.026 -03:00 [INF] [TIMING] ContractResult event concluído às 22:05:47.026 (duração: 0.2306ms)
2025-09-01 22:07:28.041 -03:00 [INF] Application is shutting down...
2025-09-01 22:11:58.547 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 22:11:59.509 -03:00 [INF] Hosting environment: Production
2025-09-01 22:11:59.510 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 22:11:59.779 -03:00 [INF] Conectando à API Deriv...
2025-09-01 22:12:00.373 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 22:12:00.753 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:12:00.756 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:12:00.773 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:12:00.773 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:12:01.271 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:12:01.272 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:12:01.272 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:12:01.272 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:12:01.272 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:12:01.272 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:12:01.272 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:12:01.272 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:12:01.272 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:12:01.274 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:12:01.384 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:12:01.384 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:12:01.384 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:12:01.384 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:12:01.385 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:12:01.385 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:12:01.385 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:12:01.385 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:12:01.385 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:12:01.385 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:12:23.553 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:12:23.553 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 22:12:23.561 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:12:26.709 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:12:26.709 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 22:12:26.991 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:12:26.991 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 22:12:27.110 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:12:27.110 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.4
2025-09-01 22:12:27.538 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:12:27.538 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:12:29.665 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:12:29.666 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:12:33.474 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 22:12:33.476 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:12:33.476
2025-09-01 22:12:33.476 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:12:33.686 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:33.694 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:33.694 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:33.695 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:33.697 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:33.698 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 220.8916ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:12:33.698 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:12:33.698 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 22:12:37.266 -03:00 [INF] [DEBUG] Contrato comprado: 292948422428, subscrevendo para atualizações
2025-09-01 22:12:37.266 -03:00 [INF] Compra executada com sucesso. ContractId: 292948422428, TransactionId: 583518737268
2025-09-01 22:12:37.267 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:12:37.267
2025-09-01 22:12:37.267 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:12:37.267
2025-09-01 22:12:37.267 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:12:37.456 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:37.464 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:37.464 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:37.473 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:37.474 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:12:37.474 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 206.6017ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:12:37.474 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:12:37.474 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 206.7953ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:12:37.474 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:12:37.474 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:12:37.474 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:12:51.193 -03:00 [INF] [TIMING] FALLBACK - Contrato 292948422428 detectado como finalizado às 22:12:51.193
2025-09-01 22:12:51.193 -03:00 [INF] Contract 292948422428 finished: Profit=0.36, Win=True
2025-09-01 22:12:51.193 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:12:51.193
2025-09-01 22:12:51.193 -03:00 [INF] [TIMING] Contract WIN at 22:12:51.193 - calling OnContractWin
2025-09-01 22:12:51.194 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:12:51.194 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:12:51.195 -03:00 [INF] Profit Table updated for contract 292948422428: Profit=0.36, ExitPrice=6012.952, ExitTime=22:12:51
2025-09-01 22:12:51.195 -03:00 [INF] [TIMING] ContractResult event concluído às 22:12:51.195 (duração: 1.9754ms)
2025-09-01 22:16:33.820 -03:00 [INF] Application is shutting down...
2025-09-01 22:17:32.980 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 22:17:33.681 -03:00 [INF] Hosting environment: Production
2025-09-01 22:17:33.681 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 22:17:33.949 -03:00 [INF] Conectando à API Deriv...
2025-09-01 22:17:34.512 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 22:17:34.859 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:17:34.863 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:17:34.994 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:17:34.994 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:17:35.327 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:17:35.328 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:17:35.328 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:17:35.328 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:17:35.328 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:17:35.328 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:17:35.328 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:17:35.328 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:17:35.328 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:17:35.330 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:17:35.358 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:17:35.358 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:17:35.359 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:17:35.359 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:17:35.359 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:17:35.359 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:17:35.359 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:17:35.359 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:17:35.359 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:17:35.359 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:17:54.319 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:17:54.320 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 22:17:54.328 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:17:56.393 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:17:56.393 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 22:17:58.415 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:17:58.415 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 22:17:58.690 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:17:58.691 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 22:17:58.765 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:17:58.765 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.4
2025-09-01 22:17:59.756 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:17:59.757 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:18:02.477 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:18:02.477 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:18:04.387 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 22:18:04.389 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:18:04.388
2025-09-01 22:18:04.389 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:18:04.559 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:04.566 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:04.566 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:04.580 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:04.581 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:04.583 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 193.4665ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:18:04.583 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:18:04.583 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 22:18:06.249 -03:00 [INF] [DEBUG] Contrato comprado: 292948705928, subscrevendo para atualizações
2025-09-01 22:18:06.249 -03:00 [INF] Compra executada com sucesso. ContractId: 292948705928, TransactionId: 583519294488
2025-09-01 22:18:06.251 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:18:06.251
2025-09-01 22:18:06.251 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:18:06.251
2025-09-01 22:18:06.251 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:18:06.589 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:06.604 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:06.605 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:06.615 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:06.615 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:18:06.615 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 364.3031ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:18:06.615 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:18:06.615 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 364.5053ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:18:06.615 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:18:06.615 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:18:06.615 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:18:10.980 -03:00 [INF] [TIMING] FALLBACK - Contrato 292948705928 detectado como finalizado às 22:18:10.980
2025-09-01 22:18:10.980 -03:00 [INF] Contract 292948705928 finished: Profit=-0.4, Win=False
2025-09-01 22:18:10.980 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:18:10.980
2025-09-01 22:18:10.981 -03:00 [INF] [TIMING] Contract LOSS at 22:18:10.981 - ZERO-DELAY EXECUTION
2025-09-01 22:18:10.982 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.1292ms. Level: 0 → 1, Stake: 0.80
2025-09-01 22:18:10.982 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.1827ms. Level: 1
2025-09-01 22:18:10.982 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:18:10.982
2025-09-01 22:18:10.982 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.8264ms
2025-09-01 22:18:10.982 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.9113ms
2025-09-01 22:18:10.983 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:18:10.983
2025-09-01 22:18:10.983 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:18:10.984 -03:00 [INF] Profit Table updated for contract 292948705928: Profit=-0.4, ExitPrice=6013.513, ExitTime=22:18:10
2025-09-01 22:18:10.984 -03:00 [INF] [TIMING] ContractResult event concluído às 22:18:10.984 (duração: 4.1964ms)
2025-09-01 22:18:11.165 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 182.6816ms. Stake: 25.60, ProposalId: bef9694e-80f5-4208-4e10-8efe62f90ca3
2025-09-01 22:18:11.166 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 183.3852ms às 22:18:11.166
2025-09-01 22:18:11.166 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0849ms às 22:18:11.166
2025-09-01 22:18:11.166 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 183.4701ms - TRUE INSTANT execution
2025-09-01 22:18:11.187 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 204.6003ms. Stake: 25.60, ProposalId: bef9694e-80f5-4208-4e10-8efe62f90ca3
2025-09-01 22:18:11.187 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 189.4168ms. Stake: 25.60, ProposalId: bef9694e-80f5-4208-4e10-8efe62f90ca3
2025-09-01 22:18:11.187 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 204.6131ms. Stake: 25.60, ProposalId: bef9694e-80f5-4208-4e10-8efe62f90ca3
2025-09-01 22:18:11.187 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 204.732ms. Stake: 25.60, ProposalId: bef9694e-80f5-4208-4e10-8efe62f90ca3
2025-09-01 22:18:11.188 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 204.8499ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-01 22:18:11.188 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-01 22:18:11.389 -03:00 [INF] Buy response processed: Contract 292948710228, Type: Unknown, Stake: 0.8
2025-09-01 22:18:11.389 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292948710228, Type: Unknown, PurchaseTime: 01:26:20
2025-09-01 22:18:16.976 -03:00 [INF] [TIMING] FALLBACK - Contrato 292948710228 detectado como finalizado às 22:18:16.976
2025-09-01 22:18:16.976 -03:00 [INF] Contract 292948710228 finished: Profit=0.76, Win=True
2025-09-01 22:18:16.976 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:18:16.976
2025-09-01 22:18:16.976 -03:00 [INF] [TIMING] Contract WIN at 22:18:16.976 - calling OnContractWin
2025-09-01 22:18:16.977 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:18:16.977 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:18:16.978 -03:00 [INF] Profit Table updated for contract 292948710228: Profit=0.76, ExitPrice=6013.501, ExitTime=22:18:16
2025-09-01 22:18:16.978 -03:00 [INF] [TIMING] ContractResult event concluído às 22:18:16.978 (duração: 1.8195ms)
2025-09-01 22:23:20.776 -03:00 [INF] Application is shutting down...
2025-09-01 22:24:09.180 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 22:24:09.208 -03:00 [INF] Hosting environment: Production
2025-09-01 22:24:09.208 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 22:24:09.581 -03:00 [INF] Conectando à API Deriv...
2025-09-01 22:24:10.167 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 22:24:10.577 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:24:10.580 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:24:10.716 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:24:10.717 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:24:11.077 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:24:11.078 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:24:11.078 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:24:11.078 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:24:11.078 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:24:11.078 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:24:11.078 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:24:11.078 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:24:11.078 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:24:11.080 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:24:11.116 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:24:31.104 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:31.105 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 22:24:31.112 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:24:33.265 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:33.265 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 22:24:36.228 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:36.228 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 22:24:36.640 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:36.640 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 22:24:36.744 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:36.744 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.4
2025-09-01 22:24:37.193 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:37.194 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:24:39.630 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:39.631 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:24:41.892 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 22:24:41.893 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:24:41.893
2025-09-01 22:24:41.893 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:24:42.105 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:42.112 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:42.112 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:42.112 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:42.120 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:42.122 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 227.752ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:24:42.122 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:24:42.122 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 22:24:43.603 -03:00 [INF] [DEBUG] Contrato comprado: 292949021248, subscrevendo para atualizações
2025-09-01 22:24:43.603 -03:00 [INF] Compra executada com sucesso. ContractId: 292949021248, TransactionId: 583519917828
2025-09-01 22:24:43.604 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:24:43.604
2025-09-01 22:24:43.604 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:24:43.604
2025-09-01 22:24:43.604 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:24:43.795 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:43.795 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:43.795 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:43.796 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:43.796 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:24:43.796 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 191.6901ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:24:43.796 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:24:43.796 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 191.8884ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:24:43.796 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:24:43.796 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:43.796 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:24:48.955 -03:00 [INF] [TIMING] FALLBACK - Contrato 292949021248 detectado como finalizado às 22:24:48.955
2025-09-01 22:24:48.955 -03:00 [INF] Contract 292949021248 finished: Profit=0.36, Win=True
2025-09-01 22:24:48.955 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:24:48.955
2025-09-01 22:24:48.955 -03:00 [INF] [TIMING] Contract WIN at 22:24:48.955 - calling OnContractWin
2025-09-01 22:24:48.956 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:24:48.956 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:24:48.957 -03:00 [INF] Profit Table updated for contract 292949021248: Profit=0.36, ExitPrice=6015.328, ExitTime=22:24:48
2025-09-01 22:24:48.957 -03:00 [INF] [TIMING] ContractResult event concluído às 22:24:48.957 (duração: 2.5202ms)
2025-09-01 22:27:32.818 -03:00 [INF] Application is shutting down...
2025-09-01 22:27:39.932 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 22:27:39.964 -03:00 [INF] Hosting environment: Production
2025-09-01 22:27:39.964 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 22:27:40.296 -03:00 [INF] Conectando à API Deriv...
2025-09-01 22:27:40.677 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 22:27:41.438 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:27:41.441 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:27:41.691 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:27:41.693 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:27:41.693 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:27:41.693 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:27:41.694 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:27:41.694 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:27:41.694 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:27:41.694 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:27:41.694 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:27:41.697 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:27:41.740 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:27:41.740 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:27:41.973 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:27:41.974 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:29:17.882 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:17.882 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 22:29:17.889 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:29:20.012 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:20.012 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-01 22:29:22.605 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:22.605 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 22:29:22.850 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:22.851 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 22:29:22.983 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:22.983 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.4
2025-09-01 22:29:23.466 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:23.466 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:29:25.610 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:25.610 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:29:29.095 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 22:29:29.096 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:29.096
2025-09-01 22:29:29.096 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:29:29.281 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:29.288 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:29.289 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:29.289 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:29.289 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:29.290 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 192.9764ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:29:29.290 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:29:29.290 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 22:29:31.578 -03:00 [INF] [DEBUG] Contrato comprado: 292949253388, subscrevendo para atualizações
2025-09-01 22:29:31.578 -03:00 [INF] Compra executada com sucesso. ContractId: 292949253388, TransactionId: 583520374148
2025-09-01 22:29:31.580 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:29:31.580
2025-09-01 22:29:31.580 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:31.580
2025-09-01 22:29:31.580 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:29:31.771 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:31.772 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:31.772 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:31.772 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:31.775 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:29:31.776 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 195.5028ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:29:31.776 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:29:31.776 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 195.8951ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:29:31.776 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:29:31.776 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:31.776 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:29:36.969 -03:00 [INF] [TIMING] FALLBACK - Contrato 292949253388 detectado como finalizado às 22:29:36.969
2025-09-01 22:29:36.969 -03:00 [INF] Contract 292949253388 finished: Profit=0.36, Win=True
2025-09-01 22:29:36.969 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:29:36.969
2025-09-01 22:29:36.970 -03:00 [INF] [TIMING] Contract WIN at 22:29:36.970 - calling OnContractWin
2025-09-01 22:29:36.970 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:36.970 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:29:36.971 -03:00 [INF] Profit Table updated for contract 292949253388: Profit=0.36, ExitPrice=6015.389, ExitTime=22:29:36
2025-09-01 22:29:36.972 -03:00 [INF] [TIMING] ContractResult event concluído às 22:29:36.972 (duração: 2.246ms)
2025-09-01 22:29:47.641 -03:00 [INF] [DEBUG] Contrato comprado: 292949264908, subscrevendo para atualizações
2025-09-01 22:29:47.641 -03:00 [INF] Compra executada com sucesso. ContractId: 292949264908, TransactionId: 583520397388
2025-09-01 22:29:47.641 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:29:47.641
2025-09-01 22:29:47.641 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:47.641
2025-09-01 22:29:47.641 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:29:47.833 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 191.2328ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:29:47.833 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 191.4052ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:29:47.840 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 198.1936ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:29:47.840 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 198.2794ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:29:47.840 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 198.2714ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:29:47.840 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 198.544ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-01 22:29:47.840 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:29:47.840 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 198.7032ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:29:47.840 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-01 22:29:47.840 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:47.840 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:29:52.982 -03:00 [INF] [TIMING] FALLBACK - Contrato 292949264908 detectado como finalizado às 22:29:52.982
2025-09-01 22:29:52.982 -03:00 [INF] Contract 292949264908 finished: Profit=0.36, Win=True
2025-09-01 22:29:52.982 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:29:52.982
2025-09-01 22:29:52.982 -03:00 [INF] [TIMING] Contract WIN at 22:29:52.982 - calling OnContractWin
2025-09-01 22:29:52.983 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:29:52.983 -03:00 [INF] Profit Table updated for contract 292949264908: Profit=0.36, ExitPrice=6014.729, ExitTime=22:29:52
2025-09-01 22:29:52.983 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:29:52.983 -03:00 [INF] [TIMING] ContractResult event concluído às 22:29:52.983 (duração: 0.5112ms)
2025-09-01 22:29:59.976 -03:00 [INF] [DEBUG] Contrato comprado: 292949275448, subscrevendo para atualizações
2025-09-01 22:29:59.976 -03:00 [INF] Compra executada com sucesso. ContractId: 292949275448, TransactionId: 583520418988
2025-09-01 22:29:59.976 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:29:59.976
2025-09-01 22:29:59.976 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:59.976
2025-09-01 22:29:59.976 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:30:00.168 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 189.9629ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:00.168 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 191.5837ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:00.180 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 204.2928ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:00.181 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 204.4422ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:00.181 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 204.5366ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:00.181 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 204.7368ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-01 22:30:00.181 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:30:00.181 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 204.8772ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:30:00.181 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-01 22:30:00.181 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:30:00.181 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:30:05.072 -03:00 [INF] [TIMING] FALLBACK - Contrato 292949275448 detectado como finalizado às 22:30:05.072
2025-09-01 22:30:05.072 -03:00 [INF] Contract 292949275448 finished: Profit=-0.4, Win=False
2025-09-01 22:30:05.072 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:30:05.072
2025-09-01 22:30:05.072 -03:00 [INF] [TIMING] Contract LOSS at 22:30:05.072 - ZERO-DELAY EXECUTION
2025-09-01 22:30:05.073 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0446ms. Level: 0 → 1, Stake: 0.80
2025-09-01 22:30:05.073 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0918ms. Level: 1
2025-09-01 22:30:05.074 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:30:05.074
2025-09-01 22:30:05.074 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.6895ms
2025-09-01 22:30:05.074 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.6683ms
2025-09-01 22:30:05.074 -03:00 [INF] Profit Table updated for contract 292949275448: Profit=-0.4, ExitPrice=6014.346, ExitTime=22:30:05
2025-09-01 22:30:05.074 -03:00 [INF] [TIMING] ContractResult event concluído às 22:30:05.074 (duração: 1.8187ms)
2025-09-01 22:30:05.074 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:30:05.074
2025-09-01 22:30:05.074 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:30:05.275 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 201.1192ms às 22:30:05.275
2025-09-01 22:30:05.275 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1112ms às 22:30:05.275
2025-09-01 22:30:05.275 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 201.2304ms - TRUE INSTANT execution
2025-09-01 22:30:05.277 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 203.1138ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:05.279 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.2748ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:05.281 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 207.2945ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:05.281 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 207.3135ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:05.282 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 207.0113ms. Stake: 25.60, ProposalId: 9e228625-a4bf-7653-6e1b-7f207515d456
2025-09-01 22:30:05.282 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 208.5311ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-01 22:30:05.282 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-01 22:30:05.494 -03:00 [INF] Buy response processed: Contract 292949280068, Type: Unknown, Stake: 0.8
2025-09-01 22:30:05.494 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292949280068, Type: Unknown, PurchaseTime: 01:38:14
2025-09-01 22:30:11.121 -03:00 [INF] [TIMING] FALLBACK - Contrato 292949280068 detectado como finalizado às 22:30:11.121
2025-09-01 22:30:11.121 -03:00 [INF] Contract 292949280068 finished: Profit=0.76, Win=True
2025-09-01 22:30:11.121 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:30:11.121
2025-09-01 22:30:11.121 -03:00 [INF] [TIMING] Contract WIN at 22:30:11.121 - calling OnContractWin
2025-09-01 22:30:11.121 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:30:11.121 -03:00 [INF] Profit Table updated for contract 292949280068: Profit=0.76, ExitPrice=6014.679, ExitTime=22:30:11
2025-09-01 22:30:11.121 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:30:11.122 -03:00 [INF] [TIMING] ContractResult event concluído às 22:30:11.122 (duração: 0.2525ms)
2025-09-01 22:35:51.146 -03:00 [WRN] Conexão perdida: Lost
2025-09-01 22:35:51.733 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-09-01 22:35:52.222 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:35:52.223 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:35:52.649 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:35:52.649 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:35:52.663 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-09-01 22:35:52.665 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:35:52.665 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:35:52.665 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:35:52.665 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:35:52.665 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:35:52.665 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:35:52.665 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:35:53.086 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-09-01 22:35:53.194 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:35:53.194 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-09-01 22:35:53.194 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: PUTE
2025-09-01 22:35:53.194 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=PUTE
2025-09-01 22:35:53.195 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:40:19.021 -03:00 [INF] Application is shutting down...
2025-09-01 22:40:32.600 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 22:40:32.633 -03:00 [INF] Hosting environment: Production
2025-09-01 22:40:32.633 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 22:40:33.126 -03:00 [INF] Conectando à API Deriv...
2025-09-01 22:40:33.794 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 22:40:34.380 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:40:34.442 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:40:34.929 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:40:34.929 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:40:34.929 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:40:34.930 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:40:34.930 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:40:34.931 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:40:34.931 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:40:34.931 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:40:34.931 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:40:34.931 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:40:34.931 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:40:34.933 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:40:35.151 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:40:35.151 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:40:35.151 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:40:35.152 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:40:35.152 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:40:35.152 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:40:35.152 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:40:35.152 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:40:35.152 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:40:35.154 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:40:44.451 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:40:44.452 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=1.00
2025-09-01 22:40:44.458 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:40:49.731 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:40:49.731 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=1.00
2025-09-01 22:40:51.895 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 22:40:51.897 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:40:51.897
2025-09-01 22:40:51.897 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:40:52.084 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:52.091 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:52.093 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:52.094 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:52.109 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:52.110 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 212.4693ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:40:52.110 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:40:52.110 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 22:40:53.337 -03:00 [INF] [DEBUG] Contrato comprado: 292949809768, subscrevendo para atualizações
2025-09-01 22:40:53.337 -03:00 [INF] Compra executada com sucesso. ContractId: 292949809768, TransactionId: 583521374648
2025-09-01 22:40:53.338 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:40:53.338
2025-09-01 22:40:53.338 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:40:53.338
2025-09-01 22:40:53.338 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:40:53.533 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:53.534 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:53.534 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:53.534 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:53.535 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:40:53.535 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 196.7131ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:40:53.535 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:40:53.536 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 197.2291ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:40:53.536 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:40:53.536 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:40:53.536 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=1.00
2025-09-01 22:41:07.003 -03:00 [INF] [TIMING] FALLBACK - Contrato 292949809768 detectado como finalizado
2025-09-01 22:41:07.003 -03:00 [INF] Contract 292949809768 finished: Profit=0.95, Win=True
2025-09-01 22:41:07.003 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:41:07.003
2025-09-01 22:41:07.004 -03:00 [INF] [TIMING] Contract WIN at 22:41:07.004 - calling OnContractWin
2025-09-01 22:41:07.004 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:41:07.004 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=1.00
2025-09-01 22:41:07.005 -03:00 [INF] Profit Table updated for contract 292949809768: Profit=0.95, ExitPrice=6010.615, ExitTime=01:49:14
2025-09-01 22:41:07.006 -03:00 [INF] [TIMING] ContractResult event concluído às 22:41:07.006 (duração: 2.3151ms)
2025-09-01 22:43:41.627 -03:00 [INF] Application is shutting down...
2025-09-01 22:45:36.837 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 22:45:36.859 -03:00 [INF] Hosting environment: Production
2025-09-01 22:45:36.860 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 22:45:37.228 -03:00 [INF] Conectando à API Deriv...
2025-09-01 22:45:37.886 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 22:45:38.337 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:45:38.341 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:45:38.363 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:45:38.364 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:45:38.851 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:45:38.851 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:45:38.852 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:45:38.852 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:45:38.852 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:45:38.852 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:45:38.852 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:45:38.852 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:45:38.852 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:45:38.857 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:45:38.949 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:45:38.950 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:45:47.271 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:45:47.271 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=1.00
2025-09-01 22:45:47.279 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:45:51.987 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 22:45:51.992 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:45:51.991
2025-09-01 22:45:51.992 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:45:52.188 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:52.198 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:52.199 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:52.204 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:52.799 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:52.802 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 808.3553ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:45:52.802 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:45:52.802 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 22:45:53.594 -03:00 [INF] [DEBUG] Contrato comprado: 292950050188, subscrevendo para atualizações
2025-09-01 22:45:53.594 -03:00 [INF] Compra executada com sucesso. ContractId: 292950050188, TransactionId: 583521819528
2025-09-01 22:45:53.596 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:45:53.596
2025-09-01 22:45:53.596 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:45:53.596
2025-09-01 22:45:53.596 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:45:53.796 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:53.796 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:53.796 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:53.796 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:53.806 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:45:53.807 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 210.7057ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:45:53.807 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:45:53.807 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 211.0756ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:45:53.807 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:45:53.807 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:45:53.807 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=1.00
2025-09-01 22:46:06.995 -03:00 [INF] [TIMING] FALLBACK - Contrato 292950050188 detectado como finalizado
2025-09-01 22:46:06.996 -03:00 [INF] Contract 292950050188 finished: Profit=-1, Win=False
2025-09-01 22:46:06.996 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:46:06.996
2025-09-01 22:46:06.996 -03:00 [INF] [TIMING] Contract LOSS at 22:46:06.996 - ZERO-DELAY EXECUTION
2025-09-01 22:46:06.998 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.353ms. Level: 0 → 1, Stake: 2.00
2025-09-01 22:46:06.998 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.4422ms. Level: 1
2025-09-01 22:46:06.999 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:46:06.999
2025-09-01 22:46:06.999 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 1.3868ms
2025-09-01 22:46:06.999 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 2.602ms
2025-09-01 22:46:06.999 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:46:06.999
2025-09-01 22:46:06.999 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:46:07.001 -03:00 [INF] Profit Table updated for contract 292950050188: Profit=-1, ExitPrice=6010.538, ExitTime=01:54:15
2025-09-01 22:46:07.001 -03:00 [INF] [TIMING] ContractResult event concluído às 22:46:07.001 (duração: 5.1723ms)
2025-09-01 22:46:07.203 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 203.116ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:07.206 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 206.0161ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:07.206 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 206.2378ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:07.206 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 206.2522ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:07.217 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 217.7268ms às 22:46:07.217
2025-09-01 22:46:07.217 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1018ms às 22:46:07.217
2025-09-01 22:46:07.217 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 217.8286ms - TRUE INSTANT execution
2025-09-01 22:46:07.242 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 232.5137ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:07.242 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 242.5977ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-01 22:46:07.242 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-01 22:46:07.438 -03:00 [INF] Buy response processed: Contract 292950062268, Type: Unknown, Stake: 2
2025-09-01 22:46:07.439 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292950062268, Type: Unknown, PurchaseTime: 01:54:16
2025-09-01 22:46:21.100 -03:00 [INF] [TIMING] FALLBACK - Contrato 292950062268 detectado como finalizado
2025-09-01 22:46:21.100 -03:00 [INF] Contract 292950062268 finished: Profit=-2, Win=False
2025-09-01 22:46:21.100 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:46:21.100
2025-09-01 22:46:21.100 -03:00 [INF] [TIMING] Contract LOSS at 22:46:21.100 - ZERO-DELAY EXECUTION
2025-09-01 22:46:21.100 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0544ms. Level: 1 → 2, Stake: 4.00
2025-09-01 22:46:21.100 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0804ms. Level: 2
2025-09-01 22:46:21.100 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:46:21.100
2025-09-01 22:46:21.100 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1983ms
2025-09-01 22:46:21.100 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.2185ms
2025-09-01 22:46:21.100 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:46:21.100
2025-09-01 22:46:21.100 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:46:21.101 -03:00 [INF] Profit Table updated for contract 292950062268: Profit=-2, ExitPrice=6010.69, ExitTime=01:54:28
2025-09-01 22:46:21.101 -03:00 [INF] [TIMING] ContractResult event concluído às 22:46:21.101 (duração: 1.0769ms)
2025-09-01 22:46:21.306 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.9971ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:21.307 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 206.2192ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:21.326 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.0906ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:21.326 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.2718ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:21.326 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 225.479ms às 22:46:21.326
2025-09-01 22:46:21.326 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0345ms às 22:46:21.326
2025-09-01 22:46:21.326 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 225.5135ms - TRUE INSTANT execution
2025-09-01 22:46:21.326 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.3718ms. Stake: 64.00, ProposalId: 64c7c888-a5ea-3cab-2ce5-5cced19c9ff7
2025-09-01 22:46:21.326 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 225.4682ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-01 22:46:21.326 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-09-01 22:46:21.582 -03:00 [INF] Buy response processed: Contract 292950073068, Type: Unknown, Stake: 4
2025-09-01 22:46:21.582 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292950073068, Type: Unknown, PurchaseTime: 01:54:30
2025-09-01 22:46:35.006 -03:00 [INF] [TIMING] FALLBACK - Contrato 292950073068 detectado como finalizado
2025-09-01 22:46:35.006 -03:00 [INF] Contract 292950073068 finished: Profit=3.81, Win=True
2025-09-01 22:46:35.006 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:46:35.006
2025-09-01 22:46:35.006 -03:00 [INF] [TIMING] Contract WIN at 22:46:35.006 - calling OnContractWin
2025-09-01 22:46:35.007 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:46:35.007 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=1.00
2025-09-01 22:46:35.008 -03:00 [INF] Profit Table updated for contract 292950073068: Profit=3.81, ExitPrice=6010.266, ExitTime=01:54:42
2025-09-01 22:46:35.008 -03:00 [INF] [TIMING] ContractResult event concluído às 22:46:35.008 (duração: 2.339ms)
2025-09-01 22:47:46.927 -03:00 [INF] Application is shutting down...
2025-09-01 22:47:46.974 -03:00 [ERR] [TICKS] Error processing tick update
System.NullReferenceException: Object reference not set to an instance of an object.
   at Excalibur.ViewModels.MainViewModel.OnTickReceived(Decimal price, DateTime timestamp) in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 693
   at Excalibur.Services.DerivApiService.ProcessTickUpdate(JsonElement root) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 1030
2025-09-01 22:48:22.666 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-01 22:48:22.694 -03:00 [INF] Hosting environment: Production
2025-09-01 22:48:22.695 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-01 22:48:23.058 -03:00 [INF] Conectando à API Deriv...
2025-09-01 22:48:23.727 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-01 22:48:24.178 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:48:24.181 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:48:24.226 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-01 22:48:24.226 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-01 22:48:24.683 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:48:24.684 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:48:24.684 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:48:24.684 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:48:24.684 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:48:24.684 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:48:24.684 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:48:24.684 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:48:24.684 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:48:24.689 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:48:24.803 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-01 22:48:24.804 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-01 22:48:24.804 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-01 22:48:24.804 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-01 22:48:24.804 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-01 22:48:24.804 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-01 22:48:24.804 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-01 22:48:24.804 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-01 22:48:24.804 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-01 22:48:24.805 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-01 22:48:39.253 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:48:39.254 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 22:48:39.263 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-01 22:48:40.722 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:48:40.722 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-01 22:48:43.956 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:48:43.956 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-01 22:48:44.202 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:48:44.202 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-01 22:48:44.389 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:48:44.389 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.4
2025-09-01 22:48:45.309 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:48:45.309 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:48:47.123 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:48:47.123 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:48:49.094 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-01 22:48:49.096 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:48:49.095
2025-09-01 22:48:49.096 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:48:49.294 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:49.317 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:49.317 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:49.318 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:49.318 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:49.319 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 222.8041ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:48:49.319 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:48:49.319 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-01 22:48:51.349 -03:00 [INF] [DEBUG] Contrato comprado: 292950176208, subscrevendo para atualizações
2025-09-01 22:48:51.350 -03:00 [INF] Compra executada com sucesso. ContractId: 292950176208, TransactionId: 583522064248
2025-09-01 22:48:51.356 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:48:51.356
2025-09-01 22:48:51.356 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:48:51.356
2025-09-01 22:48:51.357 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:48:51.556 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:51.557 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:51.557 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:51.557 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:51.574 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1429
2025-09-01 22:48:51.574 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 217.5967ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-01 22:48:51.574 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-01 22:48:51.574 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 218.0193ms - propostas GARANTIDAMENTE prontas
2025-09-01 22:48:51.574 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-01 22:48:51.574 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:48:51.574 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-09-01 22:48:56.982 -03:00 [INF] [TIMING] FALLBACK - Contrato 292950176208 detectado como finalizado
2025-09-01 22:48:56.982 -03:00 [INF] Contract 292950176208 finished: Profit=-0.4, Win=False
2025-09-01 22:48:56.982 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:48:56.982
2025-09-01 22:48:56.982 -03:00 [INF] [TIMING] Contract LOSS at 22:48:56.982 - ZERO-DELAY EXECUTION
2025-09-01 22:48:56.983 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.1454ms. Level: 0 → 1, Stake: 0.80
2025-09-01 22:48:56.983 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.1939ms. Level: 1
2025-09-01 22:48:56.984 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:48:56.984
2025-09-01 22:48:56.984 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.9771ms
2025-09-01 22:48:56.984 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.9118ms
2025-09-01 22:48:56.984 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:48:56.984
2025-09-01 22:48:56.984 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:48:56.985 -03:00 [INF] Profit Table updated for contract 292950176208: Profit=-0.4, ExitPrice=6010.748, ExitTime=01:57:04
2025-09-01 22:48:56.986 -03:00 [INF] [TIMING] ContractResult event concluído às 22:48:56.985 (duração: 3.6025ms)
2025-09-01 22:48:57.187 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 202.5118ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:48:57.198 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 213.2922ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:48:57.204 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 219.1373ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:48:57.213 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 228.7101ms às 22:48:57.213
2025-09-01 22:48:57.213 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.113ms às 22:48:57.213
2025-09-01 22:48:57.213 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 228.8231ms - TRUE INSTANT execution
2025-09-01 22:48:57.213 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 228.6844ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:48:57.213 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 195.6503ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:48:57.213 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 228.8822ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-01 22:48:57.213 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-01 22:48:57.477 -03:00 [INF] Buy response processed: Contract 292950181448, Type: Unknown, Stake: 0.8
2025-09-01 22:48:57.479 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292950181448, Type: Unknown, PurchaseTime: 01:57:06
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] FALLBACK - Contrato 292950181448 detectado como finalizado
2025-09-01 22:49:02.955 -03:00 [INF] Contract 292950181448 finished: Profit=-0.8, Win=False
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:49:02.955
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] Contract LOSS at 22:49:02.955 - ZERO-DELAY EXECUTION
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0354ms. Level: 1 → 2, Stake: 1.60
2025-09-01 22:49:02.955 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0805ms. Level: 2
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:49:02.955
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1601ms
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1755ms
2025-09-01 22:49:02.955 -03:00 [INF] Profit Table updated for contract 292950181448: Profit=-0.8, ExitPrice=6010.839, ExitTime=01:57:10
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:49:02.955
2025-09-01 22:49:02.955 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-01 22:49:02.955 -03:00 [INF] [TIMING] ContractResult event concluído às 22:49:02.955 (duração: 0.5117ms)
2025-09-01 22:49:03.161 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.2062ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:49:03.161 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.3172ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:49:03.168 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 212.4499ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:49:03.168 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 212.559ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:49:03.176 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 221.3252ms às 22:49:03.176
2025-09-01 22:49:03.176 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0866ms às 22:49:03.176
2025-09-01 22:49:03.176 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 221.4118ms - TRUE INSTANT execution
2025-09-01 22:49:03.177 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 221.1647ms. Stake: 25.60, ProposalId: dd1062c3-6b66-770b-6454-2f88ac28ceb3
2025-09-01 22:49:03.177 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 221.3439ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-01 22:49:03.177 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-09-01 22:49:03.444 -03:00 [INF] Buy response processed: Contract 292950185048, Type: Unknown, Stake: 1.6
2025-09-01 22:49:03.444 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292950185048, Type: Unknown, PurchaseTime: 01:57:12
2025-09-01 22:49:09.043 -03:00 [INF] [TIMING] FALLBACK - Contrato 292950185048 detectado como finalizado
2025-09-01 22:49:09.043 -03:00 [INF] Contract 292950185048 finished: Profit=1.53, Win=True
2025-09-01 22:49:09.043 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:49:09.043
2025-09-01 22:49:09.043 -03:00 [INF] [TIMING] Contract WIN at 22:49:09.043 - calling OnContractWin
2025-09-01 22:49:09.043 -03:00 [INF] Profit Table updated for contract 292950185048: Profit=1.53, ExitPrice=6010.937, ExitTime=01:57:16
2025-09-01 22:49:09.043 -03:00 [INF] [TIMING] ContractResult event concluído às 22:49:09.043 (duração: 0.1843ms)
2025-09-01 22:49:09.043 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-01 22:49:09.043 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
