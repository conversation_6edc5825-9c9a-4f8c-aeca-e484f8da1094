# Excalibur - Binary Options Trading Bot

## Architecture Overview

**Excalibur** is a WPF trading bot for binary options using the Deriv.com API. The application implements advanced trading strategies (Martingale, Dualgale, Dualsoros) with ultra-low latency execution for high-frequency trading.

### Core Components

- **MainViewModel**: Central orchestrator (~1800 lines) handling all trading logic, state management, and API coordination
- **DerivApiService**: WebSocket-based API service for real-time trading operations  
- **ChartViewModel**: LiveCharts integration for real-time price visualization
- **Trading Strategies**: Three automated systems with different risk/reward profiles

## Trading System Architecture

### Strategy Types
```csharp
// Three main strategies with different execution patterns
IsMartingaleEnabled   // Progressive stake multiplication on losses
IsDualgaleEnabled     // Dual-entry system with percentage recovery
IsDualsorosEnabled    // Complex dual-direction trading with Soros logic
```

### Ultra-Fast Execution Pipeline
The system implements a sophisticated multi-tier execution system:

1. **Hot Proposal Pool**: Pre-calculated proposals for instant execution (`PopulateHotProposalPoolImmediate`)
2. **Zero-Delay Execution**: Direct WebSocket commands (`OnContractLossUltraFast`) 
3. **Emergency Fallback**: Market execution when pool is empty

### Critical State Management
- **Session-Based Profits**: `SessionProfit` tracks realized P/L per trading session
- **Take Profit Logic**: Automated session termination with complex gating conditions
- **Stake Rounding**: All stakes rounded to 2 decimals with minimum 0.35 enforcement

## Development Patterns

### MVVM Implementation
```csharp
// All ViewModels inherit from custom ObservableObject
public class MainViewModel : ObservableObject
{
    // Property pattern with automatic change notification
    private decimal _stake;
    public decimal Stake 
    { 
        get => _stake; 
        set { _stake = Math.Round(value, 2); OnPropertyChanged(); }
    }
}
```

### Async/Threading Patterns
- **UI Thread Safety**: All API responses use `Application.Current.Dispatcher.Invoke`
- **Background Processing**: Fire-and-forget with `_ = Task.Run(async () => ...)`
- **Ultra-Fast Paths**: Synchronous operations in critical paths for sub-100ms execution

### Logging Strategy
```csharp
// Dual logging: Console + Serilog for different audiences
Console.WriteLine($"[DUALSOROS DEBUG] Stakes calculated - Higher: {higherStake:F2}");
_logger.LogInformation($"Dualsoros stakes calculated - Higher: {higherStake:F2}");
```

## Key Conventions

### Nullable Reference Handling
- All selection properties are nullable: `SelectedActiveSymbol?`, `SelectedContractType?`
- API responses checked for null before dereferencing
- Null-forgiving operators used in verified safe contexts

### Decimal Precision
- **Stakes**: Always 2 decimal places with minimum 0.35 enforcement
- **Prices**: F5 formatting for display, F2 for calculations
- **Profits**: F2 formatting consistently across UI and logs

### Error Recovery Patterns
```csharp
// Graceful degradation with automatic recovery
try { /* primary execution */ }
catch (Exception ex) 
{ 
    _logger.LogError(ex, "Context");
    // Automatic fallback/recovery logic
    _ = Task.Run(async () => await RecoveryMethod());
}
```

## Build & Debug

### Project Setup
```bash
dotnet build Excalibur.csproj          # Build project
dotnet run                             # Start application
```

### Dependencies
- **.NET 8 Windows**: WPF application with nullable reference types enabled
- **LiveChartsCore**: Real-time charting with SkiaSharp rendering
- **Websocket.Client**: WebSocket communication for Deriv API
- **Serilog**: Structured logging to daily rolling files in `logs/`

### Debug Workflow
1. **Console Logs**: Real-time debug output with strategy-specific prefixes
2. **Log Files**: Structured logs in `logs/log-YYYYMMDD.txt` 
3. **Breakpoint Strategy**: Focus on `MainViewModel` event handlers and API service methods

## Integration Points

### Deriv API Communication
- **WebSocket Events**: Real-time contract updates, tick data, account balance
- **Request/Response**: Proposal calculation, contract purchases, symbol data
- **State Synchronization**: Portfolio tracking, active contract monitoring

### UI Data Binding
- **Two-Way Binding**: Stake amounts, strategy parameters, market selections
- **Command Binding**: BuyCommand with CanExecute logic based on connection state
- **Real-Time Updates**: Price ticks, profit table, account balance display

### Performance Optimization
- **Memory Management**: Profit table limited to 50 entries, old data purged
- **Execution Speed**: Multiple execution tiers from instant (pool) to fallback (API)
- **Threading**: Background proposal calculation, foreground UI updates

When modifying trading logic, always consider the session management, profit calculation accuracy, and stake validation to maintain system integrity.
