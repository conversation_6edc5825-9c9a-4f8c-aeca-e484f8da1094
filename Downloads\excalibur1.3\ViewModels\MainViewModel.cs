using Excalibur.Services;
using Excalibur.Models;
using Excalibur.Infrastructure;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.Logging;

namespace Excalibur.ViewModels;

public class MainViewModel : ObservableObject
{
    private readonly IDerivApiService _derivApiService;
    private readonly ILogger<MainViewModel> _logger;
    private readonly DualModeManager _dualManager;

    // Constants (SOLID: Single Responsibility)
    private const decimal MinStakeAllowed = 0.35m;

    private bool _isConnected;
    public bool IsConnected
    {
        get => _isConnected;
        set { _isConnected = value; OnPropertyChanged(); }
    }

    private string _accountCode = "-----------";
    public string AccountCode
    {
        get => _accountCode;
        set { _accountCode = value; OnPropertyChanged(); }
    }
    
    private string _accountType = "---";
    public string AccountType
    {
        get => _accountType;
        set { _accountType = value; OnPropertyChanged(); }
    }

    private double _balance;
    public double Balance
    {
        get => _balance;
        set { _balance = value; OnPropertyChanged(); }
    }

    private long _ping;
    public long Ping
    {
        get => _ping;
        set { _ping = value; OnPropertyChanged(); }
    }
    
    // Properties for Contract Info Card
    private string _selectedContractDisplayName = "Nenhum contrato selecionado";
    public string SelectedContractDisplayName
    {
        get => _selectedContractDisplayName;
        set { _selectedContractDisplayName = value; OnPropertyChanged(); }
    }
    
    // Simplified properties for compact display
    public string ContractSymbol => SelectedActiveSymbol?.Symbol ?? "---";
    public string ContractTypeDisplay => SelectedContractType != null ? $"{SelectedContractType.CategoryDisplay} - {SelectedContractType.ContractDisplay}" : "---";
    
    private decimal _currentTickPrice;
    public decimal CurrentTickPrice
    {
        get => _currentTickPrice;
        set 
        { 
            var previousPrice = _currentTickPrice;
            _currentTickPrice = value; 
            OnPropertyChanged(); 
            OnPropertyChanged(nameof(FormattedTickPrice));
            OnPropertyChanged(nameof(SpotPriceDisplay));
            
            // Update price direction
            if (previousPrice != 0 && value != previousPrice)
            {
                IsPriceUp = value > previousPrice;
                OnPropertyChanged(nameof(IsPriceUp));
                OnPropertyChanged(nameof(SpotColor));
                OnPropertyChanged(nameof(PriceArrow));
            }
        }
    }
    
    public string FormattedTickPrice
    {
        get => _currentTickPrice == 0 ? "---" : _currentTickPrice.ToString("F5");
    }
    
    // Price direction and formatting properties
    public bool IsPriceUp { get; private set; }
    
    public string SpotPriceDisplay
    {
        get
        {
            if (_currentTickPrice == 0) return "---";
            // Always show exactly 3 decimal places
            return _currentTickPrice.ToString("F3");
        }
    }
    
    public string SpotColor
    {
        get => _currentTickPrice == 0 ? "White" : (IsPriceUp ? "#FF2ECC71" : "#FFFF4444");
    }
    
    public string PriceArrow
    {
        get => _currentTickPrice == 0 ? "" : (IsPriceUp ? "▲" : "▼");
    }
    
    private DateTime _lastTickTime;
    public DateTime LastTickTime
    {
        get => _lastTickTime;
        set { _lastTickTime = value; OnPropertyChanged(); }
    }

    // Coleções para seleção de contratos
    private ObservableCollection<string> _markets = new();
    public ObservableCollection<string> Markets
    {
        get => _markets;
        set { _markets = value; OnPropertyChanged(); }
    }

    private ObservableCollection<string> _subMarkets = new();
    public ObservableCollection<string> SubMarkets
    {
        get => _subMarkets;
        set { _subMarkets = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ActiveSymbol> _activeSymbols = new();
    public ObservableCollection<ActiveSymbol> ActiveSymbols
    {
        get => _activeSymbols;
        set { _activeSymbols = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ContractDetails> _contractTypes = new();
    public ObservableCollection<ContractDetails> ContractTypes
    {
        get => _contractTypes;
        set { _contractTypes = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ProfitTableEntry> _profitTableEntries = new();
    public ObservableCollection<ProfitTableEntry> ProfitTableEntries
    {
        get => _profitTableEntries;
        set { _profitTableEntries = value; OnPropertyChanged(); }
    }

    // Propriedades para seleções atuais
    private string? _selectedMarket;
    public string? SelectedMarket
    {
        get => _selectedMarket;
        set
        {
            _selectedMarket = value;
            OnPropertyChanged();
            OnMarketSelectionChanged();
        }
    }

    private decimal CalculateNextMartingaleStake()
    {
        // Use TryGetStakeAmountDecimal to avoid culture-dependent parsing issues
        if (InitialStakeAmount == 0 && TryGetStakeAmountDecimal(out decimal initialStake))
        {
            InitialStakeAmount = initialStake;
        }

        var nextLevel = CurrentMartingaleLevel + 1;
        if (nextLevel == 1)
        {
            return InitialStakeAmount;
        }
        else
        {
            return InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, nextLevel - 1);
        }
    }

    private string? _selectedSubMarket;
    public string? SelectedSubMarket
    {
        get => _selectedSubMarket;
        set
        {
            _selectedSubMarket = value;
            OnPropertyChanged();
            OnSubMarketSelectionChanged();
        }
    }

    private ActiveSymbol? _selectedActiveSymbol;
    public ActiveSymbol? SelectedActiveSymbol
    {
        get => _selectedActiveSymbol;
        set
        {
            _selectedActiveSymbol = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(ContractSymbol));
            
            // Update chart title and clear previous data only if NOT restoring after reconnection
            if (ChartViewModel != null)
            {
                if (value != null)
                {
                    ChartViewModel.UpdateChartTitle(value.Symbol);
                    // Only clear data if this is a user-initiated change, not a restoration
                    if (!_isRestoringSelections)
                    {
                        ChartViewModel.ClearData(); // Clear previous symbol's data
                    }
                }
                else
                {
                    ChartViewModel.UpdateChartTitle(string.Empty);
                    if (!_isRestoringSelections)
                    {
                        ChartViewModel.ClearData();
                    }
                }
            }
            
            OnActiveSymbolSelectionChanged();
        }
    }

    private ContractDetails? _selectedContractType;
    public ContractDetails? SelectedContractType
    {
        get => _selectedContractType;
        set
        {
            _selectedContractType = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(ContractTypeDisplay));
            UpdateContractParameters(); // Método chave para atualizar a UI
            
            // Update contract display name and subscribe to ticks
            if (value != null && SelectedActiveSymbol != null)
            {
                // Subscribe to ticks for the selected symbol
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _derivApiService.SubscribeToTicksAsync(SelectedActiveSymbol.Symbol);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to subscribe to ticks for selected symbol");
                    }
                });
            }
            else
            {
                _derivApiService.UnsubscribeFromTicks();
            }
        }
    }

    // Novas propriedades para controlar a visibilidade da UI
    private bool _isDurationVisible;
    public bool IsDurationVisible { get => _isDurationVisible; set { _isDurationVisible = value; OnPropertyChanged(); } }

    private bool _isBarrier1Visible;
    public bool IsBarrier1Visible { get => _isBarrier1Visible; set { _isBarrier1Visible = value; OnPropertyChanged(); } }

    private bool _isBarrier2Visible;
    public bool IsBarrier2Visible { get => _isBarrier2Visible; set { _isBarrier2Visible = value; OnPropertyChanged(); } }

    private bool _isDigitSelectionVisible;
    public bool IsDigitSelectionVisible { get => _isDigitSelectionVisible; set { _isDigitSelectionVisible = value; OnPropertyChanged(); } }

    private string _durationInfo = string.Empty;
    public string DurationInfo { get => _durationInfo; set { _durationInfo = value; OnPropertyChanged(); } }

    // Propriedades para Stake e cálculo de payout
    private decimal _stake = 10.0m;
    public decimal Stake 
    { 
        get => _stake; 
        set 
        { 
            // Enforce minimum stake and round to 2 decimals (SOLID: Single Responsibility)
            _stake = Math.Round(Math.Max(value, MinStakeAllowed), 2); 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    // StakeAmount property moved to end of class to integrate with Martingale

    private string _barrier1Value = string.Empty;
    public string Barrier1Value 
    { 
        get => _barrier1Value; 
        set 
        { 
            _barrier1Value = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private string _barrier2Value = string.Empty;
    public string Barrier2Value 
    { 
        get => _barrier2Value; 
        set 
        { 
            _barrier2Value = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private int _durationValue = 5;
    public int DurationValue 
    { 
        get => _durationValue; 
        set 
        { 
            _durationValue = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private string _durationUnit = "t";
    public string DurationUnit 
    { 
        get => _durationUnit; 
        set 
        { 
            _durationUnit = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private int _selectedDigit = 0;
    public int SelectedDigit 
    { 
        get => _selectedDigit; 
        set 
        { 
            _selectedDigit = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    // Trading Control Properties (SOLID: Single Responsibility)
    private bool _isTradingEnabled = true;
    public bool IsTradingEnabled 
    { 
        get => _isTradingEnabled; 
        set 
        { 
            _isTradingEnabled = value; 
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanExecuteBuy));
            _logger.LogInformation($"Trading {(value ? "ENABLED" : "DISABLED")} by user");
        } 
    }

    // Propriedades para exibir resultados do cálculo
    private decimal _calculatedPayout;
    public decimal CalculatedPayout { get => _calculatedPayout; set { _calculatedPayout = value; OnPropertyChanged(); } }

    private decimal _askPrice;
    public decimal AskPrice
    {
        get => _askPrice;
        set { _askPrice = value; OnPropertyChanged(); }
    }

    private string? _currentProposalId;
    public string? CurrentProposalId
    {
        get => _currentProposalId;
        set { _currentProposalId = value; OnPropertyChanged(); }
    }

    private string _calculatedBarrier1 = string.Empty;
    public string CalculatedBarrier1 { get => _calculatedBarrier1; set { _calculatedBarrier1 = value; OnPropertyChanged(); } }

    private string _calculatedBarrier2 = string.Empty;
    public string CalculatedBarrier2 { get => _calculatedBarrier2; set { _calculatedBarrier2 = value; OnPropertyChanged(); } }

    private string _barrier1Suggestion = string.Empty;
    public string Barrier1Suggestion { get => _barrier1Suggestion; set { _barrier1Suggestion = value; OnPropertyChanged(); } }

    private string _barrier2Suggestion = string.Empty;
    public string Barrier2Suggestion { get => _barrier2Suggestion; set { _barrier2Suggestion = value; OnPropertyChanged(); } }

    private bool _isCalculating;
    public bool IsCalculating { get => _isCalculating; set { _isCalculating = value; OnPropertyChanged(); } }

    // Propriedades do Martingale
    private bool _isMartingaleEnabled;
    public bool IsMartingaleEnabled 
    { 
        get => _isMartingaleEnabled; 
        set 
        { 
            _isMartingaleEnabled = value; 
            OnPropertyChanged();
            if (value)
            {
                IsNoneSelected = false;
            }
            else
            {
                // Reset martingale state when disabled
                CurrentMartingaleLevel = 0;
                NextStakeAmount = TryGetStakeAmountDecimal(out decimal currentStake) ? currentStake : 0;
            }
        } 
    }

    private decimal _martingaleFactor = 2.0m;
    public decimal MartingaleFactor 
    { 
        get => _martingaleFactor; 
        set 
        { 
            // Arredonda para duas casas decimais
            _martingaleFactor = Math.Round(value, 2); 
            OnPropertyChanged();
            CalculateNextStake();
        } 
    }

    private int _martingaleLevel = 3;
    public int MartingaleLevel 
    { 
        get => _martingaleLevel; 
        set 
        { 
            _martingaleLevel = value; 
            OnPropertyChanged();
        } 
    }

    private bool _isFastMartingale;
    public bool IsFastMartingale 
    { 
        get => _isFastMartingale; 
        set 
        { 
            _isFastMartingale = value; 
            OnPropertyChanged();
            
            if (value && IsMartingaleEnabled)
            {
                _logger.LogInformation("[DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population");
                
                // IMMEDIATE AGGRESSIVE POPULATION: Ensure pool is ready instantly
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await PopulateHotProposalPoolImmediate();
                        
                        lock (_poolLock)
                        {
                            var readyLevels = _hotProposalPool.Count;
                            _logger.LogInformation($"[DEBUG] Fast Martingale READY: {readyLevels} proposals pre-calculated and ready for instant execution");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL] Fast Martingale enable: Failed to populate hot pool");
                    }
                });
            }
            else if (!value)
            {
                _logger.LogInformation("[DEBUG] Fast Martingale DISABLED - clearing hot pool");
                
                // Clear pool when disabled to save memory
                lock (_poolLock)
                {
                    _hotProposalPool.Clear();
                }
            }
        } 
    }

    // HOT PROPOSAL POOL - Pre-calculated proposals ready for instant execution
    private readonly Dictionary<int, ProposalResponse> _hotProposalPool = new();
    private readonly object _poolLock = new object();
    private bool _isPoolPopulating = false;


    private decimal _nextStakeAmount;
    public decimal NextStakeAmount 
    { 
        get => _nextStakeAmount; 
        set 
        { 
            _nextStakeAmount = value; 
            OnPropertyChanged();
        } 
    }

    private decimal _initialStakeAmount;

    // Propriedade para o radiobutton "Nenhum"
    private bool _isNoneSelected = true;
    public bool IsNoneSelected 
    { 
        get => _isNoneSelected; 
        set 
        { 
            _isNoneSelected = value; 
            OnPropertyChanged();
            if (value)
            {
                IsMartingaleEnabled = false;
                IsDualEnabled = false;
            }
        } 
    }

    // Dual mode properties
    private bool _isDualEnabled;
    public bool IsDualEnabled
    {
        get => _isDualEnabled;
        set
        {
            _isDualEnabled = value;
            OnPropertyChanged();
            if (value)
            {
                IsNoneSelected = false;
                IsMartingaleEnabled = false;
            }
        }
    }

    private decimal _dualTakeProfit;
    public decimal DualTakeProfit
    {
        get => _dualTakeProfit;
        set
        {
            _dualTakeProfit = Math.Round(value, 2);
            OnPropertyChanged();
        }
    }

    private int _dualLevel = 1;
    public int DualLevel
    {
        get => _dualLevel;
        set
        {
            _dualLevel = Math.Max(1, value); // Always at least 1
            OnPropertyChanged();
        }
    }

    private int _dualSession = 1;
    public int DualSession
    {
        get => _dualSession;
        set
        {
            _dualSession = Math.Max(1, value); // Always at least 1
            OnPropertyChanged();
        }
    }

    private decimal _dualLowerStake;
    public decimal DualLowerStake
    {
        get => _dualLowerStake;
        set
        {
            _dualLowerStake = Math.Round(value, 2);
            OnPropertyChanged();
        }
    }

    private decimal _dualHigherStake;
    public decimal DualHigherStake
    {
        get => _dualHigherStake;
        set
        {
            _dualHigherStake = Math.Round(value, 2);
            OnPropertyChanged();
        }
    }
    public decimal InitialStakeAmount 
    { 
        get => _initialStakeAmount; 
        set 
        { 
            _initialStakeAmount = value; 
            OnPropertyChanged();
        } 
    }

    private int _currentMartingaleLevel;
    public int CurrentMartingaleLevel 
    { 
        get => _currentMartingaleLevel; 
        set 
        { 
            _currentMartingaleLevel = value; 
            OnPropertyChanged();
            CalculateNextStake();
        } 
    }

    // Lista completa de símbolos ativos para filtragem
    private List<ActiveSymbol> _allActiveSymbols = new();
    
    // Flag to prevent clearing during restoration
    private bool _isRestoringSelections = false;
    
    // Chart functionality
    private ChartViewModel _chartViewModel;
    public ChartViewModel ChartViewModel
    {
        get => _chartViewModel;
        set { _chartViewModel = value; OnPropertyChanged(); }
    }
    
    // Chart zoom commands
    public ICommand ZoomToFiveMinutesCommand { get; private set; }
    public ICommand ZoomToFifteenMinutesCommand { get; private set; }
    public ICommand ZoomToOneHourCommand { get; private set; }
    public ICommand ZoomToAllCommand { get; private set; }
    
    // Chart time unit commands
    public ICommand ChartTimeUnitTicksCommand { get; private set; }
    public ICommand ChartTimeUnitSecondsCommand { get; private set; }
    public ICommand ChartTimeUnitMinutesCommand { get; private set; }
    
    public MainViewModel(IDerivApiService derivApiService, ILogger<MainViewModel> logger)
    {
        _derivApiService = derivApiService;
        _logger = logger;
        _chartViewModel = new ChartViewModel();
        _dualManager = new DualModeManager(logger);
        
        // Initialize chart zoom commands
        ZoomToFiveMinutesCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(5));
        ZoomToFifteenMinutesCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(15));
        ZoomToOneHourCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(60));
        ZoomToAllCommand = new RelayCommand(() => ChartViewModel?.ResetZoom());
        
        // Initialize chart time unit commands
        ChartTimeUnitTicksCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("ticks"));
        ChartTimeUnitSecondsCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("seconds"));
        ChartTimeUnitMinutesCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("minutes"));
        
        SubscribeToApiEvents();
        _derivApiService.ConnectAndAuthorizeAsync();
    }

    private void SubscribeToApiEvents()
    {
        _derivApiService.ConnectionEstablished += OnConnectionEstablished;
        _derivApiService.ConnectionLost += OnConnectionLost;
        _derivApiService.AccountInfoUpdated += OnAccountInfoUpdated;
        _derivApiService.PingUpdated += OnPingUpdated;
        _derivApiService.ContractResult += OnContractResultReceived;
        _derivApiService.ContractNearExpiry += OnContractNearExpiry;
        _derivApiService.TickReceived += OnTickReceived;
        _derivApiService.ContractFinished += OnContractFinished;
        _derivApiService.ContractPurchased += OnContractPurchased;
        _derivApiService.ContractEntryTickReceived += OnContractEntryTickReceived;
    }

    private void OnContractEntryTickReceived(string contractId, decimal entryTick, long entryTickTime)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId);
            if (entry != null)
            {
                // proposal_open_contract is authoritative; always update entry spot/price when received
                entry.EntryPrice = entryTick;
                entry.EntrySpot = DateTimeOffset.FromUnixTimeSeconds(entryTickTime).UtcDateTime;
                _logger.LogInformation($"Profit Table entry updated with official entry tick for contract {contractId}: {entryTick} at {entry.EntrySpot:HH:mm:ss}");
            }
        });
    }

    private void OnPingUpdated(long newPing)
    {
        Application.Current.Dispatcher.Invoke(() => Ping = newPing);
    }

    private void OnContractResultReceived(bool isWin)
    {
        var receivedTime = DateTimeOffset.Now;
        
        // ULTRA-IMMEDIATE EXECUTION: Zero overhead processing
        if (IsMartingaleEnabled)
        {
            if (isWin)
            {
                _logger.LogInformation($"[TIMING] Contract WIN at {receivedTime:HH:mm:ss.fff} - calling OnContractWin");
                // Fire-and-forget for win processing
                _ = Task.Run(() => OnContractWin());
            }
            else
            {
                _logger.LogInformation($"[TIMING] Contract LOSS at {receivedTime:HH:mm:ss.fff} - ZERO-DELAY EXECUTION");
                // ZERO-DELAY EXECUTION: Inline call with no overhead
                var executionStart = DateTimeOffset.Now;
                OnContractLossUltraFast();
                var executionEnd = DateTimeOffset.Now;
                var totalExecution = (executionEnd - executionStart).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ZERO-DELAY: Complete execution in {totalExecution}ms");
            }
        }
        else
        {
            _logger.LogInformation("[DEBUG] Martingale not enabled, ignoring contract result");
        }
    }

    private void OnContractNearExpiry(string contractId)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            _logger.LogInformation($"[DEBUG] OnContractNearExpiry chamado para contrato: {contractId}, IsMartingaleEnabled = {IsMartingaleEnabled}, IsFastMartingale = {IsFastMartingale}");
            
            // Ensure hot pool is ready for instant execution if needed
            if (IsMartingaleEnabled && IsFastMartingale)
            {
                _logger.LogInformation($"[DEBUG] Contrato próximo ao vencimento - garantindo HOT POOL pronto para execução instantânea");
                
                // Trigger immediate pool verification and replenishment if needed
                _ = Task.Run(async () =>
                {
                    lock (_poolLock)
                    {
                        var availableProposals = _hotProposalPool.Count;
                        _logger.LogInformation($"[DEBUG] HOT POOL status: {availableProposals} propostas disponíveis");
                        
                        if (availableProposals < 2)
                        {
                            _logger.LogInformation("[DEBUG] HOT POOL com poucas propostas - iniciando reabastecimento de emergencia");
                        }
                    }
                    
                    // Always ensure pool is fully populated before potential loss
                    await PopulateHotProposalPool();
                });
            }
        });
    }
    
    // Tick update event handler
    private void OnTickReceived(decimal price, DateTime timestamp)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            CurrentTickPrice = price;
            LastTickTime = timestamp;

            // Enable continuous auto-scroll to ensure latest data is always visible
            ChartViewModel?.EnableContinuousAutoScroll();

            // Update chart with new tick data
            ChartViewModel?.AddTickData(price, timestamp);

            // Update active profit table entries with current price
            UpdateActiveProfitTableEntries(price);
        });
    }


    private void OnAccountInfoUpdated(string accountCode, string accountType, double balance)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            AccountCode = accountCode;
            // O tipo de conta só vem na autorização, então não atualizamos se for nulo
            if (accountType != null) 
            {
                AccountType = accountType;
            }
            Balance = balance;
        });
    }

    private void OnConnectionLost()
    {
        Application.Current.Dispatcher.Invoke(() => IsConnected = false);
    }

    private void OnConnectionEstablished()
    {
        Application.Current.Dispatcher.Invoke(() => 
        {
            _logger.LogInformation("[DEBUG] ConnectionEstablished evento disparado");
            IsConnected = true;
            // Preserva as seleções atuais para restaurar após reconexão
            var previousMarket = SelectedMarket;
            var previousSubMarket = SelectedSubMarket;
            var previousActiveSymbol = SelectedActiveSymbol?.Symbol;
            var previousContractType = SelectedContractType?.ContractType;
            
            // Carrega os símbolos ativos quando a conexão é estabelecida
            _ = LoadActiveSymbolsAsync().ContinueWith(async _ => 
            {
                // Restaura as seleções após carregar os dados
                await RestoreSelectionsAsync(previousMarket, previousSubMarket, previousActiveSymbol, previousContractType);
                
                // Ativa subscrição para atualizações de saldo em tempo real
                await _derivApiService.SubscribeToBalanceAsync();
            });
        });
    }

    // Métodos para carregar dados da API
    private async Task LoadActiveSymbolsAsync()
    {
        try
        {
            _logger.LogInformation("[DEBUG] LoadActiveSymbolsAsync iniciado");
            var symbols = await _derivApiService.GetActiveSymbolsAsync();
            _logger.LogInformation($"[DEBUG] Recebidos {symbols.Count} símbolos ativos");
            _allActiveSymbols = symbols;
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                // Extrai mercados únicos
                var markets = symbols.Select(s => s.MarketDisplayName).Distinct().OrderBy(m => m).ToList();
                _logger.LogInformation($"[DEBUG] Extraídos {markets.Count} mercados únicos");
                Markets.Clear();
                foreach (var market in markets)
                {
                    Markets.Add(market);
                    _logger.LogInformation($"[DEBUG] Adicionado mercado: {market}");
                }
                _logger.LogInformation($"[DEBUG] Markets.Count após carregamento: {Markets.Count}");
                _logger.LogInformation($"[DEBUG] Markets contém: {string.Join(", ", Markets)}");
                
                // Força notificação de mudança na propriedade Markets
                OnPropertyChanged(nameof(Markets));
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] Erro em LoadActiveSymbolsAsync: {ex.Message}");
            // Log do erro - em uma implementação real, você usaria um logger
            System.Diagnostics.Debug.WriteLine($"Erro ao carregar símbolos ativos: {ex.Message}");
        }
    }

    private async Task RestoreSelectionsAsync(string? previousMarket, string? previousSubMarket, string? previousActiveSymbol, string? previousContractType)
    {
        try
        {
            _isRestoringSelections = true;
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                // Restaura o mercado se ainda existir
                if (!string.IsNullOrEmpty(previousMarket) && Markets.Contains(previousMarket))
                {
                    SelectedMarket = previousMarket;
                    
                    // Aguarda submercados carregarem
                    await Task.Delay(100); // Increased delay for better reliability
                    
                    // Restaura o submercado se ainda existir
                    if (!string.IsNullOrEmpty(previousSubMarket) && SubMarkets.Contains(previousSubMarket))
                    {
                        SelectedSubMarket = previousSubMarket;
                        
                        // Aguarda símbolos ativos carregarem
                        await Task.Delay(100);
                        
                        // Restaura o símbolo ativo se ainda existir
                        if (!string.IsNullOrEmpty(previousActiveSymbol))
                        {
                            var symbolToRestore = ActiveSymbols.FirstOrDefault(s => s.Symbol == previousActiveSymbol);
                            if (symbolToRestore != null)
                            {
                                SelectedActiveSymbol = symbolToRestore;
                                
                                // Aguarda tipos de contrato carregarem com mais tempo
                                await Task.Delay(200);
                                
                                // Restaura o tipo de contrato se ainda existir
                                if (!string.IsNullOrEmpty(previousContractType))
                                {
                                    // Tenta múltiplas vezes se a lista ainda estiver vazia
                                    for (int i = 0; i < 10; i++) // Increased attempts
                                    {
                                        var contractToRestore = ContractTypes.FirstOrDefault(c => c.ContractType == previousContractType);
                                        if (contractToRestore != null)
                                        {
                                            SelectedContractType = contractToRestore;
                                            _logger.LogInformation($"[DEBUG] Tipo de contrato restaurado: {previousContractType}");
                                            break;
                                        }
                                        else if (ContractTypes.Count == 0 && i < 9)
                                        {
                                            _logger.LogInformation($"[DEBUG] Lista de contratos ainda vazia, tentativa {i + 1}/10");
                                            await Task.Delay(100);
                                        }
                                        else
                                        {
                                            _logger.LogWarning($"[DEBUG] Tipo de contrato {previousContractType} não encontrado na lista atual após {i + 1} tentativas");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                _logger.LogInformation($"[DEBUG] Seleções restauradas: Market={SelectedMarket}, SubMarket={SelectedSubMarket}, Symbol={SelectedActiveSymbol?.Symbol}, ContractType={SelectedContractType?.ContractType}");
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] Erro ao restaurar seleções: {ex.Message}");
        }
        finally
        {
            _isRestoringSelections = false;
        }
    }

    // Métodos para lógica de seleção em cascata
    private void OnMarketSelectionChanged()
    {
        if (string.IsNullOrEmpty(SelectedMarket))
        {
            if (!_isRestoringSelections)
            {
                SubMarkets.Clear();
                ActiveSymbols.Clear();
                ContractTypes.Clear();
            }
            return;
        }

        // Filtra submercados baseado no mercado selecionado
        var subMarkets = _allActiveSymbols
            .Where(s => s.MarketDisplayName == SelectedMarket)
            .Select(s => s.SubmarketDisplayName)
            .Distinct()
            .OrderBy(sm => sm)
            .ToList();

        SubMarkets.Clear();
        foreach (var subMarket in subMarkets)
        {
            SubMarkets.Add(subMarket);
        }

        // Limpa seleções subsequentes apenas se não estiver restaurando
        if (!_isRestoringSelections)
        {
            SelectedSubMarket = null;
            ActiveSymbols.Clear();
            ContractTypes.Clear();
        }
    }

    private void OnSubMarketSelectionChanged()
    {
        if (string.IsNullOrEmpty(SelectedSubMarket))
        {
            if (!_isRestoringSelections)
            {
                ActiveSymbols.Clear();
                ContractTypes.Clear();
            }
            return;
        }

        // Filtra símbolos ativos baseado no submercado selecionado
        var symbols = _allActiveSymbols
            .Where(s => s.MarketDisplayName == SelectedMarket && s.SubmarketDisplayName == SelectedSubMarket)
            .OrderBy(s => s.DisplayName)
            .ToList();

        ActiveSymbols.Clear();
        foreach (var symbol in symbols)
        {
            ActiveSymbols.Add(symbol);
        }

        // Limpa seleções subsequentes apenas se não estiver restaurando
        if (!_isRestoringSelections)
        {
            SelectedActiveSymbol = null;
            ContractTypes.Clear();
        }
    }

    private async void OnActiveSymbolSelectionChanged()
    {
        if (SelectedActiveSymbol == null)
        {
            if (!_isRestoringSelections)
            {
                ContractTypes.Clear();
            }
            return;
        }

        try
        {
            var contractsResponse = await _derivApiService.GetContractsForSymbolAsync(SelectedActiveSymbol.Symbol);
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                ContractTypes.Clear();
                
                // Only clear SelectedContractType if not restoring
                if (!_isRestoringSelections)
                {
                    SelectedContractType = null; // Isso vai chamar o UpdateContractParameters e limpar a UI
                }
                
                foreach (var contract in contractsResponse.Available.OrderBy(c => c.CategoryDisplay).ThenBy(c => c.ContractDisplay))
                {
                    ContractTypes.Add(contract);
                }
            });
        }
        catch (Exception ex)
        {
            // Log do erro
            System.Diagnostics.Debug.WriteLine($"Erro ao carregar contratos para {SelectedActiveSymbol.Symbol}: {ex.Message}");
        }
    }

    // Lógica para atualizar a UI com base no contrato selecionado
    private void UpdateContractParameters()
    {
        if (SelectedContractType == null)
        {
            IsDurationVisible = false;
            IsBarrier1Visible = false;
            IsBarrier2Visible = false;
            IsDigitSelectionVisible = false;
            DurationInfo = string.Empty;
            Barrier1Suggestion = string.Empty;
            Barrier2Suggestion = string.Empty;
            return;
        }

        // Reset
        IsBarrier1Visible = false;
        IsBarrier2Visible = false;
        IsDigitSelectionVisible = false;
        Barrier1Suggestion = string.Empty;
        Barrier2Suggestion = string.Empty;

        // Sempre mostrar duração
        IsDurationVisible = true;
        DurationInfo = $"Duração: Min {SelectedContractType.MinContractDuration}, Max {SelectedContractType.MaxContractDuration}";

        // Verificar Barreiras e definir sugestões
        if (SelectedContractType.Barriers.HasValue)
        {
            if (SelectedContractType.Barriers >= 1)
            {
                IsBarrier1Visible = true;
                Barrier1Suggestion = GetBarrierSuggestion(SelectedContractType.ContractType, 1);
            }
            if (SelectedContractType.Barriers == 2)
            {
                IsBarrier2Visible = true;
                Barrier2Suggestion = GetBarrierSuggestion(SelectedContractType.ContractType, 2);
            }
        }

        // Verificar Dígitos
        if (SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
        {
            IsDigitSelectionVisible = true;
        }

        // Recalcular proposta quando parâmetros mudarem
        CalculateProposalAsync();
    }

    private string GetBarrierSuggestion(string contractType, int barrierNumber)
    {
        return contractType?.ToUpper() switch
        {
            "HIGHER" => "+10.5",
            "LOWER" => "-10.5",
            "TOUCH" => "+15.0",
            "NOTOUCH" => "+20.0",
            "STAYS_IN" when barrierNumber == 1 => "+25.0",
            "STAYS_IN" when barrierNumber == 2 => "-25.0",
            "GOES_OUT" when barrierNumber == 1 => "+30.0",
            "GOES_OUT" when barrierNumber == 2 => "-30.0",
            "ENDS_IN" when barrierNumber == 1 => "+20.0",
            "ENDS_IN" when barrierNumber == 2 => "-20.0",
            "ENDS_OUT" when barrierNumber == 1 => "+35.0",
            "ENDS_OUT" when barrierNumber == 2 => "-35.0",
            _ => "+10.0"
        };
    }

    private async Task CalculateProposalAndBuyAsync()
    {
        var methodStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] CalculateProposalAndBuyAsync iniciado às {methodStartTime:HH:mm:ss.fff}");
        
        if (SelectedContractType == null || SelectedActiveSymbol == null)
        {
            _logger.LogInformation("[TIMING] CalculateProposalAndBuyAsync cancelado - dados insuficientes");
            return;
        }

        try
        {
            var requestPrepTime = DateTimeOffset.Now;
            // Prepare proposal request with minimal overhead
            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = Math.Round(Math.Max(TryGetStakeAmountDecimal(out var _sTmp) ? _sTmp : 0m, MinStakeAllowed), 2),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }

            var requestReadyTime = DateTimeOffset.Now;
            var prepDelay = (requestReadyTime - requestPrepTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] Request preparado às {requestReadyTime:HH:mm:ss.fff} (prep delay: {prepDelay}ms)");

            // Use SendFastRequestAsync for ultra-low latency when in Fast Martingale mode
            var response = IsFastMartingale ? 
                await _derivApiService.GetFastProposalAsync(request) : 
                await _derivApiService.GetProposalAsync(request);
            
            var proposalReceivedTime = DateTimeOffset.Now;
            var proposalDelay = (proposalReceivedTime - requestReadyTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] Proposta recebida às {proposalReceivedTime:HH:mm:ss.fff} (API delay: {proposalDelay}ms)");

            if (response.Error == null && response.Proposal != null)
            {
                var uiUpdateStartTime = DateTimeOffset.Now;
                // Update UI with proposal data (minimal assignments)
                CalculatedPayout = response.Proposal.Payout;
                AskPrice = response.Proposal.AskPrice;
                CurrentProposalId = response.Proposal.Id;
                CalculatedBarrier1 = response.Proposal.Barrier ?? response.Proposal.HighBarrier ?? string.Empty;
                CalculatedBarrier2 = response.Proposal.LowBarrier ?? string.Empty;

                var uiUpdateEndTime = DateTimeOffset.Now;
                var uiUpdateDelay = (uiUpdateEndTime - uiUpdateStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] UI atualizada às {uiUpdateEndTime:HH:mm:ss.fff} (UI delay: {uiUpdateDelay}ms)");

                // Execute buy immediately without additional checks for maximum speed
                if (IsConnected && AskPrice > 0 && !string.IsNullOrEmpty(CurrentProposalId))
                {
                    var buyStartTime = DateTimeOffset.Now;
                    _logger.LogInformation($"[TIMING] Iniciando compra às {buyStartTime:HH:mm:ss.fff}. ProposalId: {CurrentProposalId}, Price: {AskPrice}");
                    await ExecuteBuyCommand();
                    
                    var buyEndTime = DateTimeOffset.Now;
                    var buyDelay = (buyEndTime - buyStartTime).TotalMilliseconds;
                    var totalDelay = (buyEndTime - methodStartTime).TotalMilliseconds;
                    _logger.LogInformation($"[TIMING] Compra concluída às {buyEndTime:HH:mm:ss.fff} (buy delay: {buyDelay}ms, total: {totalDelay}ms)");
                }
                else
                {
                    _logger.LogInformation($"[TIMING] Compra cancelada - condições não atendidas. IsConnected: {IsConnected}, AskPrice: {AskPrice}, ProposalId: {CurrentProposalId}");
                }
            }
            else
            {
                _logger.LogInformation($"[TIMING] Proposta inválida recebida. Error: {response.Error?.Message}");
            }
        }
        catch (Exception ex)
        {
            var errorTime = DateTimeOffset.Now;
            var errorDelay = (errorTime - methodStartTime).TotalMilliseconds;
            _logger.LogError(ex, $"[TIMING] Erro em CalculateProposalAndBuyAsync às {errorTime:HH:mm:ss.fff} (após {errorDelay}ms)");
        }
    }

    private async void CalculateProposalAsync()
    {
        _logger.LogInformation("[DEBUG] CalculateProposalAsync chamado");
        
        if (SelectedContractType == null || SelectedActiveSymbol == null || IsCalculating)
        {
            _logger.LogInformation($"[DEBUG] Saindo: SelectedContractType={SelectedContractType?.ContractType}, SelectedActiveSymbol={SelectedActiveSymbol?.Symbol}, IsCalculating={IsCalculating}");
            return;
        }

        // Verificar se todos os campos obrigatórios estão preenchidos
        if (IsBarrier1Visible && string.IsNullOrWhiteSpace(Barrier1Value))
        {
            _logger.LogInformation($"[DEBUG] Saindo: Barrier1 obrigatória mas vazia. IsBarrier1Visible={IsBarrier1Visible}, Barrier1Value='{Barrier1Value}'");
            return;
        }
        if (IsBarrier2Visible && string.IsNullOrWhiteSpace(Barrier2Value))
        {
            _logger.LogInformation($"[DEBUG] Saindo: Barrier2 obrigatória mas vazia. IsBarrier2Visible={IsBarrier2Visible}, Barrier2Value='{Barrier2Value}'");
            return;
        }
        if (DurationValue <= 0)
        {
            _logger.LogInformation($"[DEBUG] Saindo: DurationValue inválida: {DurationValue}");
            return;
        }
        if (string.IsNullOrWhiteSpace(DurationUnit))
        {
            _logger.LogInformation($"[DEBUG] Saindo: DurationUnit vazia: '{DurationUnit}'");
            return;
        }
        if (!TryGetStakeAmountDecimal(out decimal stakeValue) || stakeValue <= 0)
        {
            _logger.LogInformation($"[DEBUG] Saindo: StakeAmount inválido: '{StakeAmount}'");
            return;
        }
        
        _logger.LogInformation($"[DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType={SelectedContractType.ContractType}, Symbol={SelectedActiveSymbol.Symbol}, Stake={stakeValue}");

        try
        {
            IsCalculating = true;

            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = Math.Round(Math.Max(stakeValue, MinStakeAllowed), 2),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };
            
            // Só incluir LastDigitPrediction se for necessário para este tipo de contrato
            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }
            // Não definir LastDigitPrediction quando não necessário - deixar como não definido

            var response = await _derivApiService.GetProposalAsync(request);

            if (response.Error != null)
            {
                _logger.LogWarning($"Erro na proposta: {response.Error.Message}");
                CalculatedPayout = 0;
                AskPrice = 0;
                CalculatedBarrier1 = string.Empty;
                CalculatedBarrier2 = string.Empty;
            }
            else if (response.Proposal != null)
            {
                CalculatedPayout = response.Proposal.Payout;
                AskPrice = response.Proposal.AskPrice;
                CurrentProposalId = response.Proposal.Id;
                CalculatedBarrier1 = response.Proposal.Barrier ?? response.Proposal.HighBarrier ?? string.Empty;
                CalculatedBarrier2 = response.Proposal.LowBarrier ?? string.Empty;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao calcular proposta");
            CalculatedPayout = 0;
            AskPrice = 0;
            CalculatedBarrier1 = string.Empty;
            CalculatedBarrier2 = string.Empty;
        }
        finally
        {
            IsCalculating = false;
        }
    }

    // Comando para compra
    private ICommand? _buyCommand;
    public ICommand BuyCommand
    {
        get
        {
            return _buyCommand ??= new RelayCommand(async () => await ExecuteBuyCommand(), CanExecuteBuy);
        }
    }

    // Comando para parar (SOLID: Single Responsibility)
    private ICommand? _stopCommand;
    public ICommand StopCommand
    {
        get
        {
            return _stopCommand ??= new RelayCommand(ExecuteStopCommand, CanExecuteStop);
        }
    }

    private bool CanExecuteBuy()
    {
        return IsConnected && 
               IsTradingEnabled &&
               SelectedActiveSymbol != null && 
               SelectedContractType != null && 
               Stake > 0 && 
               DurationValue > 0 && 
               !string.IsNullOrEmpty(DurationUnit) &&
               AskPrice > 0 &&
               !string.IsNullOrEmpty(CurrentProposalId);
    }

    private bool CanExecuteStop()
    {
        return IsTradingEnabled; // Can stop only if trading is currently enabled
    }

    private void ExecuteStopCommand()
    {
        IsTradingEnabled = false;
        _logger.LogInformation("STOP command executed - Trading disabled");
    }

    private async Task ExecuteBuyCommand()
    {
        try
        {
            // Check if CurrentProposalId is not null before making the purchase
            if (CurrentProposalId == null || SelectedContractType == null)
            {
                _logger.LogError("Cannot buy contract: CurrentProposalId or SelectedContractType is null");
                return;
            }

            // Handle Dual mode execution
            if (IsDualEnabled)
            {
                await ExecuteDualEntry(SelectedContractType.ContractType);
                return;
            }

            // Regular buy execution
            var buyResponse = await _derivApiService.BuyContractAsync(CurrentProposalId, AskPrice);

            if (buyResponse.Error != null)
            {
                _logger.LogError("Erro na compra: {ErrorMessage}", buyResponse.Error.Message);
                return;
            }

            if (buyResponse.Buy != null)
            {
                _logger.LogInformation("Compra executada com sucesso. ContractId: {ContractId}, TransactionId: {TransactionId}",
                                      buyResponse.Buy.ContractId, buyResponse.Buy.TransactionId);

                // Add entry to Profit Table
                AddProfitTableEntry(buyResponse.Buy);

                // CRITICAL: IMMEDIATE SYNCHRONOUS HOT POOL POPULATION
                // Must populate pool NOW before any potential loss occurs
                if (IsMartingaleEnabled && IsFastMartingale)
                {
                    var preCalcStartTime = DateTimeOffset.Now;
                    _logger.LogInformation($"[TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às {preCalcStartTime:HH:mm:ss.fff}");

                    // SYNCHRONOUS population - block until complete to ensure proposals are ready
                    try
                    {
                        await PopulateHotProposalPoolImmediate();

                        var preCalcEndTime = DateTimeOffset.Now;
                        var preCalcDuration = (preCalcEndTime - preCalcStartTime).TotalMilliseconds;
                        _logger.LogInformation($"[TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em {preCalcDuration}ms - propostas GARANTIDAMENTE prontas");

                        lock (_poolLock)
                        {
                            _logger.LogInformation($"[DEBUG] HOT POOL STATUS: {_hotProposalPool.Count} propostas prontas nos níveis: [{string.Join(", ", _hotProposalPool.Keys)}]");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL] IMMEDIATE SYNC HOT POOL: ERRO CRÍTICO no pré-cálculo síncrono");
                    }
                }

                // Recalcular proposta automaticamente para manter o botão pronto
                CalculateProposalAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar compra");
        }
    }

    // Métodos do Martingale
    private void CalculateNextStake()
    {
        if (!IsMartingaleEnabled)
        {
            NextStakeAmount = TryGetStakeAmountDecimal(out decimal currentStake) ? currentStake : 0;
            return;
        }

        if (InitialStakeAmount == 0 && TryGetStakeAmountDecimal(out decimal initialStake))
        {
            InitialStakeAmount = initialStake;
        }

        if (CurrentMartingaleLevel == 0)
        {
            NextStakeAmount = InitialStakeAmount;
        }
        else
        {
            NextStakeAmount = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, CurrentMartingaleLevel);
        }
    }

    // Helper seguro para tentar obter decimal a partir de StakeAmount
    private bool TryGetStakeAmountDecimal(out decimal result)
    {
        result = 0m;
        if (string.IsNullOrWhiteSpace(StakeAmount)) return false;
        var normalized = StakeAmount.Replace(',', '.');
        if (decimal.TryParse(normalized, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out decimal parsed))
        {
            result = parsed;
            return true;
        }
        return false;
    }

    // IMMEDIATE HOT PROPOSAL POOL - Synchronous population for instant execution
    private async Task PopulateHotProposalPoolImmediate()
    {
        if (_isPoolPopulating || !IsFastMartingale || !IsMartingaleEnabled) return;
        
        _isPoolPopulating = true;
        var poolStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at {poolStartTime:HH:mm:ss.fff}");
        
        try
        {
            // Validate required fields
            if (SelectedContractType?.ContractType == null || SelectedActiveSymbol?.Symbol == null)
            {
                _logger.LogWarning("[DEBUG] HOT POOL IMMEDIATE: Missing required fields - canceling population");
                return;
            }
            
            // AGGRESSIVE POPULATION: Clear and rebuild entire pool for freshness
            lock (_poolLock)
            {
                _hotProposalPool.Clear();
                _logger.LogInformation("[DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild");
            }
            
            // PRE-EMPTIVE POPULATION: Calculate for ALL possible next levels (1-5)
            var populationTasks = new List<Task>();
            
            for (int level = 1; level <= 5; level++)
            {
                var levelTask = Task.Run(async () =>
                {
                    try
                    {
                        var levelStartTime = DateTimeOffset.Now;
                        var futureStake = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, level);
                        
                        var request = new ProposalRequest
                        {
                            ContractType = SelectedContractType.ContractType,
                            Symbol = SelectedActiveSymbol.Symbol,
                            Duration = DurationValue,
                            DurationUnit = DurationUnit,
                            Currency = "USD",
                            Stake = futureStake,
                            Barrier = IsBarrier1Visible ? Barrier1Value : null,
                            Barrier2 = IsBarrier2Visible ? Barrier2Value : null
                        };

                        if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
                        {
                            request.LastDigitPrediction = SelectedDigit;
                        }
                        
                        // FAST PROPOSAL REQUEST: Use high-speed API
                        var response = await _derivApiService.GetFastProposalAsync(request);
                        
                        if (response?.Error == null && response?.Proposal != null)
                        {
                            lock (_poolLock)
                            {
                                _hotProposalPool[level] = response;
                            }
                            
                            var levelEndTime = DateTimeOffset.Now;
                            var levelDelay = (levelEndTime - levelStartTime).TotalMilliseconds;
                            _logger.LogInformation($"[TIMING] HOT POOL AGGRESSIVE: Level {level} populated in {levelDelay}ms. Stake: {futureStake:F2}, ProposalId: {response.Proposal.Id}");
                        }
                        else
                        {
                            _logger.LogError($"[DEBUG] HOT POOL AGGRESSIVE: Error populating level {level}: {response?.Error?.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[DEBUG] HOT POOL AGGRESSIVE: Exception in level {level}");
                    }
                });
                
                populationTasks.Add(levelTask);
            }
            
            // PARALLEL EXECUTION: Wait for all levels to complete
            await Task.WhenAll(populationTasks);
            
            var poolEndTime = DateTimeOffset.Now;
            var totalPoolTime = (poolEndTime - poolStartTime).TotalMilliseconds;
            
            lock (_poolLock)
            {
                _logger.LogInformation($"[TIMING] HOT POOL AGGRESSIVE: Population completed in {totalPoolTime}ms. Pool contains {_hotProposalPool.Count} proposals GUARANTEED ready. Levels: [{string.Join(", ", _hotProposalPool.Keys)}]");
                
                // VALIDATION: Ensure we have at least the next 2 levels ready
                var nextLevel = CurrentMartingaleLevel + 1;
                var hasNextLevel = _hotProposalPool.ContainsKey(nextLevel);
                var hasSecondLevel = _hotProposalPool.ContainsKey(nextLevel + 1);
                
                if (!hasNextLevel || !hasSecondLevel)
                {
                    _logger.LogWarning($"[DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: {hasNextLevel}, Second: {hasSecondLevel}. Current: {CurrentMartingaleLevel}");
                }
                else
                {
                    _logger.LogInformation($"[DEBUG] HOT POOL VALIDATION: Critical levels ready. Pool is OPTIMAL for instant execution.");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DEBUG] HOT POOL IMMEDIATE: Critical error in aggressive population");
        }
        finally
        {
            _isPoolPopulating = false;
        }
    }

    // LEGACY: Async hot proposal pool for backward compatibility
    private async Task PopulateHotProposalPool()
    {
        // Redirect to immediate synchronous version for consistency
        await PopulateHotProposalPoolImmediate();
    }
    
    // Replenish used proposal in the pool (optimized for background execution)
    private async Task ReplenishHotProposal(int level)
    {
        try
        {
            if (!IsFastMartingale || !IsMartingaleEnabled) return;
            
            var replenishStartTime = DateTimeOffset.Now;
            var futureStake = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, level);
            
            var request = new ProposalRequest
            {
                ContractType = SelectedContractType!.ContractType,
                Symbol = SelectedActiveSymbol!.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = futureStake,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }
            
            var response = await _derivApiService.GetFastProposalAsync(request);
            
            if (response?.Error == null && response?.Proposal != null)
            {
                lock (_poolLock)
                {
                    _hotProposalPool[level] = response;
                }
                
                var replenishEndTime = DateTimeOffset.Now;
                var replenishDelay = (replenishEndTime - replenishStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] HOT POOL REPLENISH: Nível {level} reabastecido em {replenishDelay}ms. ProposalId: {response.Proposal.Id}");
            }
            else
            {
                _logger.LogError($"[DEBUG] HOT POOL REPLENISH: Erro ao reabastecer nível {level}: {response?.Error?.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] HOT POOL REPLENISH: Erro ao reabastecer nível {level}");
        }
    }

    // ULTRA-FAST LOSS EXECUTION: Zero-overhead processing for true sub-100ms execution
    private void OnContractLossUltraFast()
    {
        var startTime = DateTimeOffset.Now;
        
        // CRITICAL: Check if trading is enabled first (SOLID: Dependency Inversion)
        if (!IsTradingEnabled)
        {
            _logger.LogInformation("[TRADING STOPPED] Contract loss ignored - trading disabled by STOP command");
            return;
        }
        
        // VALIDATION: Ultra-fast checks with minimal overhead
        if (!IsMartingaleEnabled || CurrentMartingaleLevel >= MartingaleLevel) 
        {
            if (CurrentMartingaleLevel >= MartingaleLevel)
            {
                ResetMartingale();
            }
            return;
        }

        // INSTANT STATE UPDATE: No property change notifications during critical path
        var previousLevel = CurrentMartingaleLevel;
        CurrentMartingaleLevel++;
        var newStake = NextStakeAmount.ToString("F2");
        _stakeAmount = newStake; // Direct field access to avoid property overhead
        
        if (!IsFastMartingale)
        {
            // Update UI for manual mode after state change
            Application.Current.Dispatcher.BeginInvoke(new Action(() => {
                OnPropertyChanged(nameof(StakeAmount));
            }), System.Windows.Threading.DispatcherPriority.Background);
            return;
        }

        var stateUpdateTime = DateTimeOffset.Now;
        var stateDelay = (stateUpdateTime - startTime).TotalMilliseconds;
        _logger.LogInformation($"[TIMING] ULTRA-FAST: State updated in {stateDelay}ms. Level: {previousLevel} → {CurrentMartingaleLevel}, Stake: {newStake}");

        // ZERO-OVERHEAD POOL ACCESS: Direct dictionary access without try-catch overhead
        ProposalResponse? hotProposal = null;
        bool proposalFound = false;
        
        // Ultra-fast lock-free access when possible
        if (_hotProposalPool.ContainsKey(CurrentMartingaleLevel))
        {
            lock (_poolLock)
            {
                if (_hotProposalPool.TryGetValue(CurrentMartingaleLevel, out hotProposal))
                {
                    _hotProposalPool.Remove(CurrentMartingaleLevel);
                    proposalFound = true;
                }
            }
        }
        
        var retrievalTime = DateTimeOffset.Now;
        var retrievalDelay = (retrievalTime - stateUpdateTime).TotalMilliseconds;
        
        if (proposalFound && hotProposal?.Error == null && hotProposal?.Proposal != null)
        {
            _logger.LogInformation($"[TIMING] ULTRA-FAST: Hot proposal retrieved in {retrievalDelay}ms. ProposalId: {hotProposal.Proposal.Id}");
            
            // DIRECT WEBSOCKET EXECUTION: Bypass all intermediate layers
            var proposalId = hotProposal!.Proposal!.Id;
            var askPrice = hotProposal!.Proposal!.AskPrice;
            
            // IMMEDIATE WEBSOCKET SEND: Direct JSON construction and send
            var buyJson = $"{{\"buy\":\"{proposalId}\",\"price\":{askPrice},\"subscribe\":1}}";
            
            try
            {
                // Direct WebSocket access through service
                _derivApiService.SendDirectBuyCommand(buyJson);
                
                var executionTime = DateTimeOffset.Now;
                var totalTime = (executionTime - startTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ULTRA-FAST: Buy command sent in {totalTime}ms total - TRUE SUB-100MS EXECUTION");
                
                // MINIMAL UI UPDATE: Deferred to background with minimal data
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    CurrentProposalId = proposalId;
                    AskPrice = askPrice;
                    CalculatedPayout = hotProposal!.Proposal!.Payout;
                    OnPropertyChanged(nameof(StakeAmount)); // Update UI after execution
                }), System.Windows.Threading.DispatcherPriority.Background);
                
                // BACKGROUND REPLENISHMENT: Fire-and-forget
                _ = Task.Run(() => ReplenishHotProposal(CurrentMartingaleLevel));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CRITICAL] ULTRA-FAST: Direct WebSocket send failed - falling back to standard method");
                if (proposalId != null)
                {
                    _derivApiService.BuyContractImmediateAsync(proposalId, askPrice, _ => { });
                }
            }
        }
        else
        {
            var emergencyTime = DateTimeOffset.Now;
            var emergencyDelay = (emergencyTime - startTime).TotalMilliseconds;
            _logger.LogError($"[TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in {emergencyDelay}ms. Level: {CurrentMartingaleLevel}");
            
            // EMERGENCY FALLBACK: Direct execution with minimal overhead
            var emergencyRequest = new ProposalRequest
            {
                ContractType = SelectedContractType!.ContractType,
                Symbol = SelectedActiveSymbol!.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                // Use NextStakeAmount (decimal) which was computed earlier to avoid culture parsing
                Stake = Math.Round(Math.Max(NextStakeAmount > 0 ? NextStakeAmount : (TryGetStakeAmountDecimal(out var _tmp) ? _tmp : 0m), MinStakeAllowed), 2),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                emergencyRequest.LastDigitPrediction = SelectedDigit;
            }
            
            _derivApiService.BuyInstantMarketAsync(emergencyRequest, _ => { });
            
            var emergencyExecutionTime = DateTimeOffset.Now;
            var emergencyTotalTime = (emergencyExecutionTime - startTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] ULTRA-FAST EMERGENCY: Fallback sent in {emergencyTotalTime}ms");
            
            // Update UI after emergency execution
            Application.Current.Dispatcher.BeginInvoke(new Action(() => {
                OnPropertyChanged(nameof(StakeAmount));
            }), System.Windows.Threading.DispatcherPriority.Background);
            
            // EMERGENCY POOL REBUILD: Background
            _ = Task.Run(() => PopulateHotProposalPoolImmediate());
        }
    }

    // IMMEDIATE LOSS EXECUTION: Zero-delay processing for fast martingale
    private void OnContractLossImmediate()
    {
        // Redirect to ultra-fast implementation for maximum performance
        OnContractLossUltraFast();
    }

    // LEGACY: Keep for backward compatibility
    public void OnContractLoss()
    {
        OnContractLossImmediate();
    }

    public void OnContractWin()
    {
        if (!IsMartingaleEnabled) return;
        
        // Reset to initial stake after a win
        ResetMartingale();
    }

    private void ResetMartingale()
    {
        CurrentMartingaleLevel = 0;
        StakeAmount = InitialStakeAmount.ToString("F2");
        CalculateNextStake();
    }

    private void ClearProfitTable()
    {
        ProfitTableEntries.Clear();
        _logger.LogInformation("Profit Table limpa");
    }

    private async Task ExecuteDualEntry(string contractType)
    {
        try
        {
            _logger.LogInformation("[DUAL] ExecuteDualEntry iniciado");
            _logger.LogInformation($"[DUAL] Tipo de contrato selecionado: {contractType}");

            // Validate dual mode configuration
            if (!_dualManager.IsValidConfiguration(DualTakeProfit, DualLevel, DualSession))
            {
                _logger.LogError("[DUAL] Configuração inválida para modo Dual");
                return;
            }

            string? oppositeContract;
            try
            {
                oppositeContract = _dualManager.GetOppositeContractType(contractType);
            }
            catch (ArgumentException)
            {
                _logger.LogWarning($"[DUAL] Este tipo de contrato não suporta modo Dual: {contractType}");
                return;
            }

            // Calculate stakes for this entry
            if (CalculatedPayout <= 0)
            {
                _logger.LogError("[DUAL] Payout inválido para cálculo das stakes");
                return;
            }

            if (!TryGetStakeAmountDecimal(out decimal baseStake))
            {
                _logger.LogError("[DUAL] Stake base inválido");
                return;
            }

            // Process and update stakes based on current state
            _dualManager.ProcessResult(contractType, true, baseStake, DualTakeProfit, CalculatedPayout);

            DualLowerStake = _dualManager.LowerStake;
            DualHigherStake = _dualManager.HigherStake;

            // Determine stakes for each contract
            bool isFirstLevel = _dualManager.CurrentLevel == 1;
            decimal stake1 = _dualManager.GetStakeForContract(contractType, isFirstLevel);
            decimal stake2 = _dualManager.GetStakeForContract(oppositeContract, isFirstLevel);

            // Execute both contracts
            await ExecuteContractPair(contractType, oppositeContract, stake1, stake2);

            // Check if level completion triggers session end
            if (_dualManager.CurrentLevel >= DualLevel)
            {
                // Level complete
                _logger.LogInformation($"[DUAL] Nível {_dualManager.CurrentLevel}/{DualLevel} concluído");

                // Clear profit table and start new session if needed
                ClearProfitTable();
                _dualManager.ResetSession();

                if (_dualManager.CurrentSession >= DualSession)
                {
                    // All sessions complete
                    _logger.LogInformation("[DUAL] Todas as sessões concluídas");
                    IsDualEnabled = false; // Disable trading
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DUAL] Erro ao executar entrada dupla");
        }
    }

    private async Task ExecuteContractPair(string contract1, string contract2, decimal stake1, decimal stake2)
    {
        _logger.LogInformation($"[DUAL] Executando par de contratos: {contract1}({stake1:F2}) | {contract2}({stake2:F2})");

        try
        {
            // Get proposal and execute first contract
            var proposal1 = await _derivApiService.GetProposalAsync(new ProposalRequest
            {
                ContractType = contract1,
                Symbol = SelectedActiveSymbol?.Symbol ?? string.Empty,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Barrier = GetCurrentBarrierValue(1),
                Barrier2 = GetCurrentBarrierValue(2),
                Stake = stake1,
                LastDigitPrediction = SelectedDigit
            });

            if (proposal1?.Error != null)
            {
                _logger.LogError($"[DUAL] Erro na proposta 1: {proposal1.Error.Message}");
                return;
            }

            if (proposal1?.Proposal != null)
            {
                var buyResponse1 = await _derivApiService.BuyContractAsync(proposal1.Proposal.Id, proposal1.Proposal.AskPrice);
                if (buyResponse1?.Buy != null)
                {
                    AddProfitTableEntry(buyResponse1.Buy);
                    _logger.LogInformation($"[DUAL] Contrato 1 executado: {contract1} - ID: {buyResponse1.Buy.ContractId}");
                }
            }

            // Get proposal and execute second contract
            var proposal2 = await _derivApiService.GetProposalAsync(new ProposalRequest
            {
                ContractType = contract2,
                Symbol = SelectedActiveSymbol?.Symbol ?? string.Empty,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Barrier = GetCurrentBarrierValue(1),
                Barrier2 = GetCurrentBarrierValue(2),
                Stake = stake2, // Fixed: use Stake instead of Amount
                LastDigitPrediction = SelectedDigit // Fixed: use LastDigitPrediction instead of Selected
            });

            if (proposal2?.Error != null)
            {
                _logger.LogError($"[DUAL] Erro na proposta 2: {proposal2.Error.Message}");
                return;
            }

            if (proposal2?.Proposal != null)
            {
                var buyResponse2 = await _derivApiService.BuyContractAsync(proposal2.Proposal.Id, proposal2.Proposal.AskPrice);
                if (buyResponse2?.Buy != null)
                {
                    AddProfitTableEntry(buyResponse2.Buy);
                    _logger.LogInformation($"[DUAL] Contrato 2 executado: {contract2} - ID: {buyResponse2.Buy.ContractId}");
                }
            }

            _logger.LogInformation("[DUAL] Par de contratos executado com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DUAL] Erro ao executar par de contratos");
        }
    }

    private string GetCurrentBarrierValue(int barrierNumber)
    {
        return barrierNumber switch
        {
            1 => Barrier1Value,
            2 => Barrier2Value,
            _ => string.Empty
        };
    }

    // Override do StakeAmount para integrar com Martingale
    private string _stakeAmount = "1.00";
    public string StakeAmount
    {
        get => _stakeAmount;
        set
        {
            // Replace commas with dots for consistent decimal handling
            var normalizedInput = value.Replace(',', '.');

            // Allow empty field without enforcing minimum
            if (string.IsNullOrWhiteSpace(normalizedInput))
            {
                _stakeAmount = string.Empty;
                _stake = 0m;
                OnPropertyChanged();
                OnPropertyChanged(nameof(Stake));
                CalculateNextStake();
                // Do not calculate proposal if field is empty
                return;
            }

            // Keep raw input while editing
            _stakeAmount = normalizedInput;
            OnPropertyChanged();

            // Synchronize with Stake property if we have a valid numeric string
            if (decimal.TryParse(normalizedInput, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out decimal stakeValue))
            {
                _stake = stakeValue;
                OnPropertyChanged(nameof(Stake));
            }

            // Update initial stake if martingale is enabled and this is a manual change
            if (IsMartingaleEnabled && decimal.TryParse(normalizedInput, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out decimal stake))
            {
                if (CurrentMartingaleLevel == 0)
                {
                    InitialStakeAmount = stake;
                }
            }

            CalculateNextStake();
            CalculateProposalAsync();
        }
    }

    // Profit Table Methods (SOLID: Single Responsibility)
    private void AddProfitTableEntry(BuyContract buyContract)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            // Use only API-provided entry_tick_time and entry_tick. Do not fall back to local purchase_time or current tick.
            DateTime? entrySpotTime = null;
            if (buyContract.EntryTickTime.HasValue && buyContract.EntryTickTime.Value > 0)
            {
                entrySpotTime = DateTimeOffset.FromUnixTimeSeconds(buyContract.EntryTickTime.Value).UtcDateTime;
            }

            // Determine duration display from longcode when possible (robust regex parsing)
            string durationDisplay = GetDurationDisplayText();
            try
            {
                if (!string.IsNullOrEmpty(buyContract.LongCode))
                {
                    // Look for patterns like '1 tick', '5 ticks', '30 sec', '1 second'
                    var lc = buyContract.LongCode;
                    var match = System.Text.RegularExpressions.Regex.Match(lc, "(\\d+)\\s*(tick|ticks|second|seconds|sec|minute|minutes|min|hour|hours|hr)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                    if (match.Success)
                    {
                        var number = match.Groups[1].Value;
                        var unit = match.Groups[2].Value.ToLowerInvariant();
                        // Normalize unit
                        unit = unit switch
                        {
                            "tick" or "ticks" => (number == "1") ? "tick" : "ticks",
                            "second" or "seconds" or "sec" => (number == "1") ? "sec" : "secs",
                            "minute" or "minutes" or "min" => (number == "1") ? "min" : "mins",
                            "hour" or "hours" or "hr" => (number == "1") ? "hr" : "hrs",
                            _ => unit
                        };
                        durationDisplay = $"{number} {unit}";
                    }
                }
            }
            catch { }

            var entry = new ProfitTableEntry
            {
                RefId = buyContract.ContractId.ToString(),
                Contract = GetContractTypeDisplayName(),
                Duration = durationDisplay,
                EntrySpot = entrySpotTime, // null if API didn't provide entry_tick_time
                Stake = buyContract.BuyPrice,
                Payout = buyContract.Payout,
                // API official entry tick only. Leave null when not provided by API.
                EntryPrice = buyContract.EntryTick,
                IsActive = true
            };

            ProfitTableEntries.Insert(0, entry); // Add to top of list

            // Keep only last 50 entries to avoid memory issues
            while (ProfitTableEntries.Count > 50)
            {
                ProfitTableEntries.RemoveAt(ProfitTableEntries.Count - 1);
            }
        });
    }

    private string GetContractTypeDisplayName()
    {
        if (SelectedContractType != null)
        {
            return SelectedContractType.ContractDisplay;
        }
        return "Unknown";
    }

    private string GetDurationDisplayText()
    {
        if (!string.IsNullOrEmpty(DurationValue.ToString()) && !string.IsNullOrEmpty(DurationUnit))
        {
            var unitDisplay = DurationUnit switch
            {
                "t" => "ticks",
                "s" => "sec",
                "m" => "min",
                "h" => "hr",
                "d" => "day",
                _ => DurationUnit
            };
            return $"{DurationValue} {unitDisplay}";
        }
        return "---";
    }

    private void UpdateProfitTableEntry(string contractId, decimal currentPrice, decimal? profit = null, bool? isFinished = null)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId);
            if (entry != null)
            {
                entry.CurrentPrice = currentPrice;

                if (profit.HasValue)
                {
                    entry.TotalProfitLoss = profit.Value;
                }

                if (isFinished.HasValue && isFinished.Value)
                {
                    entry.IsActive = false;
                    // Don't set ExitSpot here - let OnContractFinished handle it with proper API time
                    entry.ExitPrice = currentPrice;
                }
            }
        });
    }

    private void UpdateActiveProfitTableEntries(decimal currentPrice)
    {
        foreach (var entry in ProfitTableEntries.Where(e => e.IsActive))
        {
            entry.CurrentPrice = currentPrice;
        }
    }

    private void OnContractFinished(string contractId, decimal profit, decimal exitPrice, DateTime exitTime)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId && e.IsActive);
            if (entry != null)
            {
                entry.TotalProfitLoss = profit;
                entry.ExitPrice = exitPrice; // API spot price at exit
                // Ensure ExitSpot is stored as UTC (API official time may be local or UTC)
                entry.ExitSpot = exitTime.ToUniversalTime(); // store as GMT
                entry.IsActive = false;

                _logger.LogInformation($"Profit Table updated for contract {contractId}: Profit={profit}, ExitPrice={exitPrice}, ExitTime={exitTime:HH:mm:ss}");

                // Handle Dual mode contract completion
                if (IsDualEnabled)
                {
                    HandleDualContractCompletion(contractId, entry.Contract, profit > 0);
                }
            }
        });
    }

    private void HandleDualContractCompletion(string contractId, string contractType, bool isWin)
    {
        try
        {
            _logger.LogInformation($"[DUAL] Contract completed - ID: {contractId}, Type: {contractType}, Win: {isWin}");

            // Process the result in the dual manager
            if (!isWin)
            {
                // Track the losing contract for next level
                _dualManager.ProcessResult(contractType, false, 0, DualTakeProfit, CalculatedPayout);
                _logger.LogInformation($"[DUAL] Losing contract tracked: {contractType}");
            }

            // Check if we should continue to next level or complete session
            if (_dualManager.CurrentLevel > DualLevel)
            {
                // Level complete - start new session
                _logger.LogInformation($"[DUAL] Level {DualLevel} completed, starting new session");

                // Clear profit table as specified in requirements
                ClearProfitTable();
                _dualManager.ResetSession();

                if (_dualManager.CurrentSession > DualSession)
                {
                    // All sessions complete
                    _logger.LogInformation("[DUAL] All sessions completed - disabling Dual mode");
                    IsDualEnabled = false;
                }
                else
                {
                    _logger.LogInformation($"[DUAL] Starting session {_dualManager.CurrentSession}/{DualSession}");
                }
            }
            else if (_dualManager.CurrentLevel <= DualLevel)
            {
                // Continue to next level - this will be triggered by the next buy command
                _logger.LogInformation($"[DUAL] Ready for level {_dualManager.CurrentLevel}/{DualLevel}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DUAL] Error handling contract completion");
        }
    }

    private void OnContractPurchased(string contractId, string contractType, string duration, decimal stake, decimal payout, DateTime purchaseTime)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = new ProfitTableEntry
            {
                RefId = contractId,
                Contract = contractType,
                Duration = duration,
                // Only use values provided by the API. Do not use local fallbacks.
                EntrySpot = null, // API should populate entry tick_time when available
                Stake = stake,
                Payout = payout,
                EntryPrice = null, // API should populate entry tick/spot when available; do not use CurrentTickPrice
                IsActive = true
            };

            ProfitTableEntries.Insert(0, entry); // Add to top of list

            // Keep only last 50 entries to avoid memory issues
            while (ProfitTableEntries.Count > 50)
            {
                ProfitTableEntries.RemoveAt(ProfitTableEntries.Count - 1);
            }

            _logger.LogInformation($"Profit Table entry added for automatic purchase - Contract: {contractId}, Type: {contractType}, PurchaseTime: {purchaseTime:HH:mm:ss}");
        });
    }
}