
Seguindo os princípios SOLID, analise todo o código, compreenda a lógica dele e, sem alterar o que já está funcionando, coloque o modo Dual, ao lado do Martingale.
O modo Dual deve funcionar assim: ao ser selecionado, exibir o campo Take Profit, que deve aceitar somente valores monetários de duas casas decimais. Deve exibir o campo Level, que deve aceitar somente números inteiros. Deve exibir um campo chamado Sessão, que deve aceitar somente números inteiros. A lógica é: fazer uma entrada dupla, ou seja, fazer a compra de 2 contratos ao mesmo tempo. Esses contratos serão opostos, sempre. Por exemplo, se escolher o tipo de contrato Call, uma entrada será Call e a outra PUT. Se escolher Odd, uma entrada será Odd e a outra Even, se escolher Rise, uma entrada será Rise e a outra Fall, e assim por diante. Sempre analise o tipo de contrato, se ele aceita o oposto. Se aceitar, deverá ser entrada dupla com contratos opostos. Outro exemplo seria o Over/Under, Digit Matches/Digit Differs, Higher/Lower. Ao fazer a primeira entrada dupla, uma das stakes deve ser ligeiramente maior que a outra para que se obtenha o lucro especificado no campo Take Profit. A escolha da stake que terá o valor maior deve ser aleatório. Para saber o valor da stake que conterá o valor maior, é necessário levar o payout em consideração. O valor da stake deve ser multiplicado pelo payout que, considerando que sempre uma entrada vai ganhar e a outra perder, deve considerar o prejuízo da outra entrada no cálculo para que, em uma vitória do contrato que está com a stake maior, o lucro obtido seja igual ao valor informado no campo Take Profit. Exemplo: usuário escolheu Take Profit de $ 0.03 e Stake de $ 0.35 Significa que, o contrato que receber a stake maior terá o valor dela calculado assim: deve-se analisar o payout do contrato, o valor da stake menor, o lucro que se quer obter (Take Profit). Com essa análise é possível determinar o valor da stake maior. Caso a primeira o contrato da stake maior perca, é preciso fazer a segunda roda de entrada duplas automaticamente, mas na segunda rodada em diante, a stake maior sempre tem que ir para o contrato que perdeu na rodada anterior e, a outra stake que ganhou no contrato anterior, tem que manter-se com o valor de stake inicial (menor), caso esteja com valor maior no contrato anterior. Esse ciclo deve se repetir por tantas vezes quanto estiver especificado no campo Level. Cada entrada dupla é considerada 1 level. 
Sempre que uma rodada terminar, é para limpar os registros da tabela Profit Table e iniciar uma nova sessão. Isso deve ser repetido tantas vezes quanto estiver especificado no campo Sessão.
Use o arquivo Instruction como referência para implementação.
