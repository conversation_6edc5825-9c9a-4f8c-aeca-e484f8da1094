using System.Text.Json.Serialization;

namespace Excalibur.Models
{
    public class ProposalRequest
    {
        public string ContractType { get; set; } = string.Empty;
        public string Symbol { get; set; } = string.Empty;
        public int Duration { get; set; }
        public string DurationUnit { get; set; } = string.Empty;
        public string Currency { get; set; } = "USD";
        public string Basis { get; set; } = "stake";
        public decimal Stake { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Barrier { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Barrier2 { get; set; }
        
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public int? LastDigitPrediction { get; set; }
    }
}