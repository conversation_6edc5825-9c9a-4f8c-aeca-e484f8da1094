2025-09-02 09:08:10.180 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 09:08:10.280 -03:00 [INF] Hosting environment: Production
2025-09-02 09:08:10.281 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 09:08:11.508 -03:00 [INF] Conectando à API Deriv...
2025-09-02 09:08:12.484 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 09:08:13.224 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 09:08:13.237 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 09:08:13.324 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 09:08:13.325 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 09:08:13.864 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 09:08:13.869 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 09:08:13.870 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 09:08:13.870 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 09:08:13.870 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 09:08:13.870 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 09:08:13.870 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 09:08:13.870 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 09:08:13.871 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 09:08:13.881 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 09:08:13.923 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 09:08:13.924 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 09:08:13.924 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 09:08:13.924 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 09:08:13.924 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 09:08:13.925 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 09:08:13.925 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 09:08:13.925 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 09:08:13.925 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 09:08:13.926 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 09:11:11.622 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:11.625 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=100
2025-09-02 09:11:11.651 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 09:11:31.861 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:31.861 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=100
2025-09-02 09:11:34.010 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:34.011 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-02 09:11:34.281 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:34.281 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-02 09:11:34.720 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:34.720 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=3
2025-09-02 09:11:34.871 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:34.871 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-02 09:11:38.532 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:38.532 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=3
2025-09-02 09:11:38.747 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:38.747 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-02 09:11:38.860 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:38.860 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-02 09:11:39.135 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:39.135 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-02 09:11:39.383 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:39.383 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=3
2025-09-02 09:11:39.514 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:11:39.514 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-02 09:12:19.081 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 09:12:19.082 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=35
2025-09-02 09:12:19.148 -03:00 [INF] Application is shutting down...
2025-09-02 09:17:48.068 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 09:17:48.187 -03:00 [INF] Hosting environment: Production
2025-09-02 09:17:48.188 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 09:17:49.161 -03:00 [INF] Conectando à API Deriv...
2025-09-02 20:01:07.148 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 20:01:07.174 -03:00 [INF] Hosting environment: Production
2025-09-02 20:01:07.175 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 20:01:07.839 -03:00 [INF] Conectando à API Deriv...
2025-09-02 20:01:21.555 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 20:01:22.082 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:01:22.086 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:01:22.392 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:01:22.392 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:01:22.602 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 20:01:22.603 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 20:01:22.603 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 20:01:22.603 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 20:01:22.603 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 20:01:22.603 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 20:01:22.603 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 20:01:22.603 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 20:01:22.603 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 20:01:22.605 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 20:01:22.754 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 20:01:22.755 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:01:45.271 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:01:45.274 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 20:01:45.286 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 20:01:46.896 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:01:46.896 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 20:01:50.300 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:01:50.300 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-02 20:01:50.576 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:01:50.576 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-02 20:01:50.654 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:01:50.654 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-02 20:01:50.818 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:01:50.818 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-02 20:01:52.806 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:01:52.806 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-02 20:01:55.651 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 20:01:55.652 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:01:55.652
2025-09-02 20:01:55.653 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:01:55.926 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:55.963 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:55.963 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:55.963 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:55.964 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:55.965 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 311.5008ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 20:01:55.965 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 20:01:55.965 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-02 20:01:58.981 -03:00 [INF] [DEBUG] Contrato comprado: 293046652288, subscrevendo para atualizações
2025-09-02 20:01:58.981 -03:00 [INF] Compra executada com sucesso. ContractId: 293046652288, TransactionId: 583710543988
2025-09-02 20:01:58.982 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 20:01:58.982
2025-09-02 20:01:58.983 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:01:58.983
2025-09-02 20:01:58.983 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:01:59.263 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:59.263 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:59.278 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:59.279 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:59.301 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:01:59.302 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 319.4843ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 20:01:59.302 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 20:01:59.302 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 319.781ms - propostas GARANTIDAMENTE prontas
2025-09-02 20:01:59.302 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-02 20:01:59.302 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:01:59.302 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-02 20:02:04.565 -03:00 [INF] [TIMING] FALLBACK - Contrato 293046652288 detectado como finalizado
2025-09-02 20:02:04.565 -03:00 [INF] Contract 293046652288 finished: Profit=-0.35, Win=False
2025-09-02 20:02:04.565 -03:00 [INF] [TIMING] Disparando ContractResult event às 20:02:04.565
2025-09-02 20:02:04.565 -03:00 [INF] [TIMING] Contract LOSS at 20:02:04.565 - ZERO-DELAY EXECUTION
2025-09-02 20:02:04.567 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.1592ms. Level: 0 → 1, Stake: 0.70
2025-09-02 20:02:04.567 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.2199ms. Level: 1
2025-09-02 20:02:04.567 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 20:02:04.567
2025-09-02 20:02:04.567 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.4554ms
2025-09-02 20:02:04.567 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.4211ms
2025-09-02 20:02:04.567 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:02:04.567
2025-09-02 20:02:04.567 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:02:04.568 -03:00 [INF] Profit Table updated for contract 293046652288: Profit=-0.35, ExitPrice=5974.034, ExitTime=23:10:12
2025-09-02 20:02:04.569 -03:00 [INF] [TIMING] ContractResult event concluído às 20:02:04.569 (duração: 3.413ms)
2025-09-02 20:02:04.876 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 309.152ms. Stake: 22.40, ProposalId: 86d43603-747a-8b48-c4fd-ba16c7741ecc
2025-09-02 20:02:04.877 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 309.8294ms às 20:02:04.877
2025-09-02 20:02:04.877 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1104ms às 20:02:04.877
2025-09-02 20:02:04.877 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 309.9398ms - TRUE INSTANT execution
2025-09-02 20:02:04.881 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 313.6394ms. Stake: 22.40, ProposalId: 86d43603-747a-8b48-c4fd-ba16c7741ecc
2025-09-02 20:02:04.881 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 313.8159ms. Stake: 22.40, ProposalId: 86d43603-747a-8b48-c4fd-ba16c7741ecc
2025-09-02 20:02:04.881 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 313.9471ms. Stake: 22.40, ProposalId: 86d43603-747a-8b48-c4fd-ba16c7741ecc
2025-09-02 20:02:04.887 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 320.2328ms. Stake: 22.40, ProposalId: 86d43603-747a-8b48-c4fd-ba16c7741ecc
2025-09-02 20:02:04.888 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 320.4082ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 20:02:04.888 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-02 20:02:05.223 -03:00 [INF] Buy response processed: Contract 293046658228, Type: Unknown, Stake: 0.7
2025-09-02 20:02:05.224 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293046658228, Type: Unknown, PurchaseTime: 23:10:14
2025-09-02 20:02:10.569 -03:00 [INF] [TIMING] FALLBACK - Contrato 293046658228 detectado como finalizado
2025-09-02 20:02:10.569 -03:00 [INF] Contract 293046658228 finished: Profit=0.66, Win=True
2025-09-02 20:02:10.569 -03:00 [INF] [TIMING] Disparando ContractResult event às 20:02:10.569
2025-09-02 20:02:10.569 -03:00 [INF] [TIMING] Contract WIN at 20:02:10.569 - calling OnContractWin
2025-09-02 20:02:10.569 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:02:10.569 -03:00 [INF] Profit Table updated for contract 293046658228: Profit=0.66, ExitPrice=5973.667, ExitTime=23:10:18
2025-09-02 20:02:10.569 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-02 20:02:10.570 -03:00 [INF] [TIMING] ContractResult event concluído às 20:02:10.570 (duração: 0.5034ms)
2025-09-02 20:02:40.438 -03:00 [WRN] Conexão perdida: Lost
2025-09-02 20:02:40.926 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-09-02 20:02:41.220 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:02:41.220 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:02:41.472 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 20:02:41.472 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 20:02:41.492 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-09-02 20:02:41.496 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 20:02:41.496 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 20:02:41.496 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 20:02:41.496 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 20:02:41.496 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 20:02:41.497 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 20:02:41.497 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 20:02:41.933 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-09-02 20:02:42.040 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:02:42.040 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-02 20:02:42.040 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: PUTE
2025-09-02 20:02:42.040 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=PUTE
2025-09-02 20:02:42.041 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 20:03:25.933 -03:00 [INF] Application is shutting down...
2025-09-02 20:38:06.644 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 20:38:06.678 -03:00 [INF] Hosting environment: Production
2025-09-02 20:38:06.679 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 20:38:07.141 -03:00 [INF] Conectando à API Deriv...
2025-09-02 20:38:18.350 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 20:38:18.995 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:38:18.998 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:38:19.505 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:38:19.506 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:38:19.873 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 20:38:19.874 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 20:38:19.874 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 20:38:19.874 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 20:38:19.874 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 20:38:19.874 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 20:38:19.874 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 20:38:19.874 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 20:38:19.874 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 20:38:19.876 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:38:20.351 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 20:38:20.351 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 20:38:20.352 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 20:38:20.352 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 20:38:20.352 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 20:38:20.352 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 20:38:20.352 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 20:38:20.352 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 20:38:20.352 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 20:38:20.352 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:38:32.203 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:32.204 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 20:38:32.212 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 20:38:34.437 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:34.437 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 20:38:37.119 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:37.120 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: 'h'
2025-09-02 20:38:38.113 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:38.113 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-02 20:38:38.396 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:38.396 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-02 20:38:38.720 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:38.720 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-02 20:38:38.933 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:38.933 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-02 20:38:41.701 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:41.701 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-02 20:38:43.606 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 20:38:43.608 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:38:43.608
2025-09-02 20:38:43.608 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:38:43.880 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:43.915 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:43.915 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:44.409 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:44.421 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:44.422 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 813.8697ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 20:38:44.423 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 20:38:44.423 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-02 20:38:49.626 -03:00 [INF] [DEBUG] Contrato comprado: 293048685788, subscrevendo para atualizações
2025-09-02 20:38:49.626 -03:00 [INF] Compra executada com sucesso. ContractId: 293048685788, TransactionId: 583714504108
2025-09-02 20:38:49.628 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 20:38:49.628
2025-09-02 20:38:49.628 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:38:49.628
2025-09-02 20:38:49.628 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:38:49.885 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:49.886 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:49.886 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:49.886 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:49.917 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass267_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1430
2025-09-02 20:38:49.918 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 290.1063ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 20:38:49.918 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 20:38:49.918 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 290.3197ms - propostas GARANTIDAMENTE prontas
2025-09-02 20:38:49.918 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-02 20:38:49.918 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:49.918 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-02 20:38:55.304 -03:00 [INF] [TIMING] FALLBACK - Contrato 293048685788 detectado como finalizado
2025-09-02 20:38:55.304 -03:00 [INF] Contract 293048685788 finished: Profit=0.31, Win=True
2025-09-02 20:38:55.304 -03:00 [INF] [TIMING] Disparando ContractResult event às 20:38:55.304
2025-09-02 20:38:55.304 -03:00 [INF] [TIMING] Contract WIN at 20:38:55.304 - calling OnContractWin
2025-09-02 20:38:55.305 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:38:55.305 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-02 20:38:55.305 -03:00 [INF] Profit Table updated for contract 293048685788: Profit=0.31, ExitPrice=5964.992, ExitTime=23:47:03
2025-09-02 20:38:55.305 -03:00 [INF] [TIMING] ContractResult event concluído às 20:38:55.305 (duração: 1.5248ms)
2025-09-02 20:39:14.998 -03:00 [INF] Application is shutting down...
2025-09-02 20:46:58.696 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 20:46:58.723 -03:00 [INF] Hosting environment: Production
2025-09-02 20:46:58.723 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 20:46:58.950 -03:00 [INF] Conectando à API Deriv...
2025-09-02 20:46:59.578 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 20:46:59.942 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:46:59.945 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:46:59.962 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:46:59.962 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:47:01.958 -03:00 [ERR] [DEBUG] Erro em LoadActiveSymbolsAsync: A requisição para a API Deriv expirou.
System.TimeoutException: A requisição para a API Deriv expirou.
   at Excalibur.Services.DerivApiService.SendRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 326
   at Excalibur.Services.DerivApiService.GetActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 365
   at Excalibur.ViewModels.MainViewModel.LoadActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 775
2025-09-02 20:47:01.966 -03:00 [ERR] [DEBUG] Erro em LoadActiveSymbolsAsync: A requisição para a API Deriv expirou.
System.TimeoutException: A requisição para a API Deriv expirou.
   at Excalibur.Services.DerivApiService.SendRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 326
   at Excalibur.Services.DerivApiService.GetActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 365
   at Excalibur.ViewModels.MainViewModel.LoadActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 775
2025-09-02 20:47:01.968 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:47:01.968 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:47:05.045 -03:00 [ERR] Erro ao processar mensagem da API.
System.Collections.Generic.KeyNotFoundException: The given key was not present in the dictionary.
   at System.Text.Json.JsonElement.GetProperty(String propertyName)
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 267
2025-09-02 20:47:05.046 -03:00 [ERR] Erro ao processar mensagem da API.
System.Collections.Generic.KeyNotFoundException: The given key was not present in the dictionary.
   at System.Text.Json.JsonElement.GetProperty(String propertyName)
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 267
2025-09-02 20:47:05.046 -03:00 [ERR] Erro ao processar mensagem da API.
System.Collections.Generic.KeyNotFoundException: The given key was not present in the dictionary.
   at System.Text.Json.JsonElement.GetProperty(String propertyName)
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 267
2025-09-02 20:47:16.915 -03:00 [INF] Application is shutting down...
2025-09-02 20:47:21.377 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 20:47:21.405 -03:00 [INF] Hosting environment: Production
2025-09-02 20:47:21.405 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 20:47:21.683 -03:00 [INF] Conectando à API Deriv...
2025-09-02 20:47:22.288 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 20:47:22.686 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:47:22.689 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:47:22.906 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:47:22.906 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:47:24.704 -03:00 [ERR] [DEBUG] Erro em LoadActiveSymbolsAsync: A requisição para a API Deriv expirou.
System.TimeoutException: A requisição para a API Deriv expirou.
   at Excalibur.Services.DerivApiService.SendRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 326
   at Excalibur.Services.DerivApiService.GetActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 365
   at Excalibur.ViewModels.MainViewModel.LoadActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 775
2025-09-02 20:47:24.714 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:47:24.906 -03:00 [ERR] [DEBUG] Erro em LoadActiveSymbolsAsync: A requisição para a API Deriv expirou.
System.TimeoutException: A requisição para a API Deriv expirou.
   at Excalibur.Services.DerivApiService.SendRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 326
   at Excalibur.Services.DerivApiService.GetActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 365
   at Excalibur.ViewModels.MainViewModel.LoadActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 775
2025-09-02 20:47:24.907 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:47:34.399 -03:00 [INF] Application is shutting down...
2025-09-02 20:47:46.116 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 20:47:46.146 -03:00 [INF] Hosting environment: Production
2025-09-02 20:47:46.147 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 20:47:46.497 -03:00 [INF] Conectando à API Deriv...
2025-09-02 20:47:46.896 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 20:47:47.575 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:47:47.578 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:47:47.831 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 20:47:47.831 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 20:47:47.832 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 20:47:47.833 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 20:47:47.833 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 20:47:47.833 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 20:47:47.833 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 20:47:47.833 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 20:47:47.833 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 20:47:47.833 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 20:47:47.833 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 20:47:47.837 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:47:48.056 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 20:47:48.056 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 20:47:48.057 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 20:47:48.057 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 20:47:48.057 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 20:47:48.057 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 20:47:48.057 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 20:47:48.057 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 20:47:48.057 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 20:47:48.057 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 20:48:05.014 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:48:05.014 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 20:48:05.021 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 20:48:07.169 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:48:07.169 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 20:48:12.507 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 20:48:12.511 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:48:12.510
2025-09-02 20:48:12.512 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:48:12.713 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:12.721 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:12.722 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:12.722 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:12.723 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:12.724 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 213.2452ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 20:48:12.724 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 20:48:12.724 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-02 20:48:14.285 -03:00 [INF] [DEBUG] Contrato comprado: 293049166768, subscrevendo para atualizações
2025-09-02 20:48:14.285 -03:00 [INF] Compra executada com sucesso. ContractId: 293049166768, TransactionId: 583715443028
2025-09-02 20:48:14.287 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 20:48:14.287
2025-09-02 20:48:14.287 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:48:14.287
2025-09-02 20:48:14.287 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:48:14.523 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:14.524 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:14.524 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:14.532 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:14.533 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1445
2025-09-02 20:48:14.533 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 245.7733ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 20:48:14.533 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 20:48:14.533 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 245.9771ms - propostas GARANTIDAMENTE prontas
2025-09-02 20:48:14.533 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-02 20:48:14.533 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:48:14.533 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 20:48:16.531 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049166768: 5965.758 at 23:56:26
2025-09-02 20:48:16.538 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049166768: 5965.758 at 23:56:26
2025-09-02 20:48:18.483 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049166768: 5965.758 at 23:56:26
2025-09-02 20:48:18.488 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049166768: 5965.758 at 23:56:26
2025-09-02 20:48:20.533 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049166768: 5965.758 at 23:56:26
2025-09-02 20:48:20.533 -03:00 [INF] [TIMING] FALLBACK - Contrato 293049166768 detectado como finalizado
2025-09-02 20:48:20.534 -03:00 [INF] Contract 293049166768 finished: Profit=-1, Win=False
2025-09-02 20:48:20.534 -03:00 [INF] [TIMING] Disparando ContractResult event às 20:48:20.534
2025-09-02 20:48:20.534 -03:00 [INF] [TIMING] Contract LOSS at 20:48:20.534 - ZERO-DELAY EXECUTION
2025-09-02 20:48:20.535 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.3396ms. Level: 0 → 1, Stake: 2.00
2025-09-02 20:48:20.535 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.3988ms. Level: 1
2025-09-02 20:48:20.536 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 20:48:20.536
2025-09-02 20:48:20.536 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.6583ms
2025-09-02 20:48:20.536 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.6747ms
2025-09-02 20:48:20.536 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:48:20.536
2025-09-02 20:48:20.536 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:48:20.537 -03:00 [INF] Profit Table updated for contract 293049166768: Profit=-1, ExitPrice=5965.595, ExitTime=23:56:30
2025-09-02 20:48:20.537 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049166768: 5965.758 at 23:56:26
2025-09-02 20:48:20.537 -03:00 [INF] [TIMING] ContractResult event concluído às 20:48:20.537 (duração: 3.4642ms)
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 213.7067ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 213.8635ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 213.8233ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 214.3647ms às 20:48:20.750
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0641ms às 20:48:20.750
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 214.4288ms - TRUE INSTANT execution
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 214.2679ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 201.1756ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:20.750 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 214.464ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 20:48:20.750 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-02 20:48:21.017 -03:00 [INF] Buy response processed: Contract 293049172508, Type: Unknown, Stake: 2
2025-09-02 20:48:21.017 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293049172508, Type: Unknown, PurchaseTime: 23:56:30
2025-09-02 20:48:22.492 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049172508: 5965.496 at 23:56:32
2025-09-02 20:48:22.503 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049172508: 5965.496 at 23:56:32
2025-09-02 20:48:24.517 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049172508: 5965.496 at 23:56:32
2025-09-02 20:48:24.517 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049172508: 5965.496 at 23:56:32
2025-09-02 20:48:26.482 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049172508: 5965.496 at 23:56:32
2025-09-02 20:48:26.482 -03:00 [INF] [TIMING] FALLBACK - Contrato 293049172508 detectado como finalizado
2025-09-02 20:48:26.482 -03:00 [INF] Contract 293049172508 finished: Profit=-2, Win=False
2025-09-02 20:48:26.482 -03:00 [INF] [TIMING] Disparando ContractResult event às 20:48:26.482
2025-09-02 20:48:26.482 -03:00 [INF] [TIMING] Contract LOSS at 20:48:26.482 - ZERO-DELAY EXECUTION
2025-09-02 20:48:26.482 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0403ms. Level: 1 → 2, Stake: 4.00
2025-09-02 20:48:26.482 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0595ms. Level: 2
2025-09-02 20:48:26.482 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 20:48:26.482
2025-09-02 20:48:26.482 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.14ms
2025-09-02 20:48:26.482 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1571ms
2025-09-02 20:48:26.483 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 20:48:26.482
2025-09-02 20:48:26.483 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 20:48:26.483 -03:00 [INF] Profit Table updated for contract 293049172508: Profit=-2, ExitPrice=5965.38, ExitTime=23:56:34
2025-09-02 20:48:26.484 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049172508: 5965.496 at 23:56:32
2025-09-02 20:48:26.484 -03:00 [INF] [TIMING] ContractResult event concluído às 20:48:26.484 (duração: 1.2835ms)
2025-09-02 20:48:26.703 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 219.9833ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:26.703 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 220.189ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:26.703 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 220.2197ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:26.703 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 201.5927ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:26.827 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 343.9308ms. Stake: 64.00, ProposalId: 8c31cfe1-5f24-acd6-6f05-641ac5c6ed46
2025-09-02 20:48:26.827 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 344.0706ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 20:48:26.827 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-09-02 20:48:26.827 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 344.2833ms às 20:48:26.827
2025-09-02 20:48:26.827 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0393ms às 20:48:26.827
2025-09-02 20:48:26.827 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 344.3226ms - TRUE INSTANT execution
2025-09-02 20:48:27.084 -03:00 [INF] Buy response processed: Contract 293049177168, Type: Unknown, Stake: 4
2025-09-02 20:48:27.084 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293049177168, Type: Unknown, PurchaseTime: 23:56:36
2025-09-02 20:48:28.501 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049177168: 5965.242 at 23:56:38
2025-09-02 20:48:28.509 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049177168: 5965.242 at 23:56:38
2025-09-02 20:48:30.499 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049177168: 5965.242 at 23:56:38
2025-09-02 20:48:30.524 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049177168: 5965.242 at 23:56:38
2025-09-02 20:48:32.491 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049177168: 5965.242 at 23:56:38
2025-09-02 20:48:32.491 -03:00 [INF] [TIMING] FALLBACK - Contrato 293049177168 detectado como finalizado
2025-09-02 20:48:32.491 -03:00 [INF] Contract 293049177168 finished: Profit=3.81, Win=True
2025-09-02 20:48:32.491 -03:00 [INF] [TIMING] Disparando ContractResult event às 20:48:32.491
2025-09-02 20:48:32.491 -03:00 [INF] [TIMING] Contract WIN at 20:48:32.491 - calling OnContractWin
2025-09-02 20:48:32.491 -03:00 [INF] Profit Table updated for contract 293049177168: Profit=3.81, ExitPrice=5965.339, ExitTime=23:56:40
2025-09-02 20:48:32.492 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 20:48:32.492 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 20:48:32.493 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049177168: 5965.242 at 23:56:38
2025-09-02 20:48:32.493 -03:00 [INF] [TIMING] ContractResult event concluído às 20:48:32.493 (duração: 1.4494ms)
2025-09-02 20:52:33.338 -03:00 [INF] Application is shutting down...
2025-09-02 21:02:42.396 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 21:02:42.425 -03:00 [INF] Hosting environment: Production
2025-09-02 21:02:42.426 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 21:02:42.707 -03:00 [INF] Conectando à API Deriv...
2025-09-02 21:02:43.425 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 21:02:43.824 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:02:43.827 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:02:43.845 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:02:43.845 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:02:44.094 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:02:44.095 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:02:44.095 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:02:44.095 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:02:44.095 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:02:44.095 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:02:44.095 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:02:44.095 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:02:44.095 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:02:44.098 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:02:44.216 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:02:44.216 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:02:44.216 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:02:44.216 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:02:44.216 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:02:44.216 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:02:44.217 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:02:44.217 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:02:44.217 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:02:44.217 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:03:07.921 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:03:07.923 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:03:07.931 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 21:03:10.856 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:03:10.856 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:03:15.620 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 21:03:15.622 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:03:15.622
2025-09-02 21:03:15.622 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:03:15.819 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:15.828 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:15.828 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:15.828 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:15.839 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:15.840 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 216.8313ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:03:15.840 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:03:15.840 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-02 21:03:17.107 -03:00 [INF] [DEBUG] Contrato comprado: 293049945448, subscrevendo para atualizações
2025-09-02 21:03:17.108 -03:00 [INF] Compra executada com sucesso. ContractId: 293049945448, TransactionId: 583716964928
2025-09-02 21:03:17.113 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:03:17.113
2025-09-02 21:03:17.113 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:03:17.113
2025-09-02 21:03:17.113 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:03:17.318 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:17.318 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:17.319 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:17.325 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:17.325 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:03:17.325 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 212.2436ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:03:17.325 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:03:17.325 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 212.5541ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:03:17.325 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-02 21:03:17.325 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:03:17.325 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:03:18.695 -03:00 [INF] [DEBUG] entry_tick for 293049945448: epoch=1756858288 -> 00:11:28.000, price=5964.874
2025-09-02 21:03:18.696 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049945448: 5964.874 at 00:11:28
2025-09-02 21:03:18.704 -03:00 [INF] [DEBUG] entry_tick for 293049945448: epoch=1756858288 -> 00:11:28.000, price=5964.874
2025-09-02 21:03:18.704 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049945448: 5964.874 at 00:11:28
2025-09-02 21:03:20.646 -03:00 [INF] [DEBUG] entry_tick for 293049945448: epoch=1756858288 -> 00:11:28.000, price=5964.874
2025-09-02 21:03:20.646 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049945448: 5964.874 at 00:11:28
2025-09-02 21:03:20.646 -03:00 [INF] [DEBUG] entry_tick for 293049945448: epoch=1756858288 -> 00:11:28.000, price=5964.874
2025-09-02 21:03:20.646 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049945448: 5964.874 at 00:11:28
2025-09-02 21:03:22.607 -03:00 [INF] [DEBUG] entry_tick for 293049945448: epoch=1756858288 -> 00:11:28.000, price=5964.874
2025-09-02 21:03:22.607 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049945448: 5964.874 at 00:11:28
2025-09-02 21:03:22.607 -03:00 [INF] [TIMING] FALLBACK - Contrato 293049945448 detectado como finalizado
2025-09-02 21:03:22.607 -03:00 [INF] Contract 293049945448 finished: Profit=0.95, Win=True
2025-09-02 21:03:22.607 -03:00 [INF] [DEBUG] exit_time for 293049945448: sell_time_epoch=1756858290 -> 00:11:30.000
2025-09-02 21:03:22.607 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:03:22.607
2025-09-02 21:03:22.608 -03:00 [INF] [TIMING] Contract WIN at 21:03:22.608 - calling OnContractWin
2025-09-02 21:03:22.609 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:03:22.609 -03:00 [INF] Profit Table updated for contract 293049945448: Profit=0.95, ExitPrice=5964.967, ExitTime=00:11:30
2025-09-02 21:03:22.609 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:03:22.611 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049945448: 5964.874 at 00:11:28
2025-09-02 21:03:22.611 -03:00 [INF] [TIMING] ContractResult event concluído às 21:03:22.611 (duração: 3.8003ms)
2025-09-02 21:03:31.096 -03:00 [INF] [DEBUG] Contrato comprado: 293049956408, subscrevendo para atualizações
2025-09-02 21:03:31.096 -03:00 [INF] Compra executada com sucesso. ContractId: 293049956408, TransactionId: 583716986048
2025-09-02 21:03:31.097 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:03:31.097
2025-09-02 21:03:31.097 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:03:31.097
2025-09-02 21:03:31.097 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:03:31.303 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.8241ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:31.303 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.9669ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:31.309 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 212.701ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:31.314 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.216ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:31.314 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.3993ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:31.314 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 217.5112ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:03:31.314 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:03:31.314 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 217.6925ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:03:31.314 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:03:31.314 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:03:31.314 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:03:32.636 -03:00 [INF] [DEBUG] entry_tick for 293049956408: epoch=1756858302 -> 00:11:42.000, price=5965.378
2025-09-02 21:03:32.636 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049956408: 5965.378 at 00:11:42
2025-09-02 21:03:32.642 -03:00 [INF] [DEBUG] entry_tick for 293049956408: epoch=1756858302 -> 00:11:42.000, price=5965.378
2025-09-02 21:03:32.642 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049956408: 5965.378 at 00:11:42
2025-09-02 21:03:34.730 -03:00 [INF] [DEBUG] entry_tick for 293049956408: epoch=1756858302 -> 00:11:42.000, price=5965.378
2025-09-02 21:03:34.730 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049956408: 5965.378 at 00:11:42
2025-09-02 21:03:34.730 -03:00 [INF] [DEBUG] entry_tick for 293049956408: epoch=1756858302 -> 00:11:42.000, price=5965.378
2025-09-02 21:03:34.730 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049956408: 5965.378 at 00:11:42
2025-09-02 21:03:36.624 -03:00 [INF] [DEBUG] entry_tick for 293049956408: epoch=1756858302 -> 00:11:42.000, price=5965.378
2025-09-02 21:03:36.624 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049956408: 5965.378 at 00:11:42
2025-09-02 21:03:36.624 -03:00 [INF] [TIMING] FALLBACK - Contrato 293049956408 detectado como finalizado
2025-09-02 21:03:36.625 -03:00 [INF] Contract 293049956408 finished: Profit=-1, Win=False
2025-09-02 21:03:36.625 -03:00 [INF] [DEBUG] exit_time for 293049956408: sell_time_epoch=1756858304 -> 00:11:44.000
2025-09-02 21:03:36.625 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:03:36.625
2025-09-02 21:03:36.625 -03:00 [INF] [TIMING] Contract LOSS at 21:03:36.625 - ZERO-DELAY EXECUTION
2025-09-02 21:03:36.627 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.2511ms. Level: 0 → 1, Stake: 2.00
2025-09-02 21:03:36.627 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.3175ms. Level: 1
2025-09-02 21:03:36.627 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:03:36.627
2025-09-02 21:03:36.629 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 2.1649ms
2025-09-02 21:03:36.629 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 4.2249ms
2025-09-02 21:03:36.629 -03:00 [INF] Profit Table updated for contract 293049956408: Profit=-1, ExitPrice=5965.319, ExitTime=00:11:44
2025-09-02 21:03:36.629 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:03:36.629
2025-09-02 21:03:36.629 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:03:36.629 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049956408: 5965.378 at 00:11:42
2025-09-02 21:03:36.629 -03:00 [INF] [TIMING] ContractResult event concluído às 21:03:36.629 (duração: 4.505ms)
2025-09-02 21:03:36.840 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 211.2102ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:36.841 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 196.1352ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:36.841 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 211.9471ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:36.844 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 216.1249ms às 21:03:36.843
2025-09-02 21:03:36.844 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1453ms às 21:03:36.844
2025-09-02 21:03:36.844 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 216.2702ms - TRUE INSTANT execution
2025-09-02 21:03:36.844 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 214.8241ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:36.858 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 229.1742ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:36.858 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 229.432ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:03:36.858 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-02 21:03:37.088 -03:00 [INF] Buy response processed: Contract 293049961028, Type: Unknown, Stake: 2
2025-09-02 21:03:37.090 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293049961028, Type: Unknown, PurchaseTime: 00:11:46
2025-09-02 21:03:38.692 -03:00 [INF] [DEBUG] entry_tick for 293049961028: epoch=1756858308 -> 00:11:48.000, price=5965.435
2025-09-02 21:03:38.692 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049961028: 5965.435 at 00:11:48
2025-09-02 21:03:38.692 -03:00 [INF] [DEBUG] entry_tick for 293049961028: epoch=1756858308 -> 00:11:48.000, price=5965.435
2025-09-02 21:03:38.693 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049961028: 5965.435 at 00:11:48
2025-09-02 21:03:40.678 -03:00 [INF] [DEBUG] entry_tick for 293049961028: epoch=1756858308 -> 00:11:48.000, price=5965.435
2025-09-02 21:03:40.678 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049961028: 5965.435 at 00:11:48
2025-09-02 21:03:40.724 -03:00 [INF] [DEBUG] entry_tick for 293049961028: epoch=1756858308 -> 00:11:48.000, price=5965.435
2025-09-02 21:03:40.725 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049961028: 5965.435 at 00:11:48
2025-09-02 21:03:42.658 -03:00 [INF] [DEBUG] entry_tick for 293049961028: epoch=1756858308 -> 00:11:48.000, price=5965.435
2025-09-02 21:03:42.658 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049961028: 5965.435 at 00:11:48
2025-09-02 21:03:42.658 -03:00 [INF] [TIMING] FALLBACK - Contrato 293049961028 detectado como finalizado
2025-09-02 21:03:42.658 -03:00 [INF] Contract 293049961028 finished: Profit=-2, Win=False
2025-09-02 21:03:42.658 -03:00 [INF] [DEBUG] exit_time for 293049961028: sell_time_epoch=1756858310 -> 00:11:50.000
2025-09-02 21:03:42.658 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:03:42.658
2025-09-02 21:03:42.658 -03:00 [INF] [TIMING] Contract LOSS at 21:03:42.658 - ZERO-DELAY EXECUTION
2025-09-02 21:03:42.658 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.039ms. Level: 1 → 2, Stake: 4.00
2025-09-02 21:03:42.658 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0533ms. Level: 2
2025-09-02 21:03:42.658 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:03:42.658
2025-09-02 21:03:42.658 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1244ms
2025-09-02 21:03:42.658 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1385ms
2025-09-02 21:03:42.658 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:03:42.658
2025-09-02 21:03:42.658 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:03:42.658 -03:00 [INF] Profit Table updated for contract 293049961028: Profit=-2, ExitPrice=5965.423, ExitTime=00:11:50
2025-09-02 21:03:42.660 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049961028: 5965.435 at 00:11:48
2025-09-02 21:03:42.660 -03:00 [INF] [TIMING] ContractResult event concluído às 21:03:42.660 (duração: 2.3304ms)
2025-09-02 21:03:42.869 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 210.5689ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:42.870 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 211.362ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:42.873 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 214.795ms às 21:03:42.873
2025-09-02 21:03:42.873 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1721ms às 21:03:42.873
2025-09-02 21:03:42.873 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 214.9671ms - TRUE INSTANT execution
2025-09-02 21:03:42.873 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 214.9308ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:42.883 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.123ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:42.884 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 214.3556ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:42.884 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 226.2206ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:03:42.884 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-09-02 21:03:43.139 -03:00 [INF] Buy response processed: Contract 293049966008, Type: Unknown, Stake: 4
2025-09-02 21:03:43.139 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293049966008, Type: Unknown, PurchaseTime: 00:11:52
2025-09-02 21:03:44.598 -03:00 [INF] [DEBUG] entry_tick for 293049966008: epoch=1756858314 -> 00:11:54.000, price=5965.521
2025-09-02 21:03:44.599 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049966008: 5965.521 at 00:11:54
2025-09-02 21:03:44.616 -03:00 [INF] [DEBUG] entry_tick for 293049966008: epoch=1756858314 -> 00:11:54.000, price=5965.521
2025-09-02 21:03:44.616 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049966008: 5965.521 at 00:11:54
2025-09-02 21:03:46.729 -03:00 [INF] [DEBUG] entry_tick for 293049966008: epoch=1756858314 -> 00:11:54.000, price=5965.521
2025-09-02 21:03:46.730 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049966008: 5965.521 at 00:11:54
2025-09-02 21:03:46.736 -03:00 [INF] [DEBUG] entry_tick for 293049966008: epoch=1756858314 -> 00:11:54.000, price=5965.521
2025-09-02 21:03:46.737 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049966008: 5965.521 at 00:11:54
2025-09-02 21:03:48.712 -03:00 [INF] [DEBUG] entry_tick for 293049966008: epoch=1756858314 -> 00:11:54.000, price=5965.521
2025-09-02 21:03:48.712 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049966008: 5965.521 at 00:11:54
2025-09-02 21:03:48.712 -03:00 [INF] [TIMING] FALLBACK - Contrato 293049966008 detectado como finalizado
2025-09-02 21:03:48.712 -03:00 [INF] Contract 293049966008 finished: Profit=-4, Win=False
2025-09-02 21:03:48.712 -03:00 [INF] [DEBUG] exit_time for 293049966008: sell_time_epoch=1756858316 -> 00:11:56.000
2025-09-02 21:03:48.712 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:03:48.712
2025-09-02 21:03:48.712 -03:00 [INF] [TIMING] Contract LOSS at 21:03:48.712 - ZERO-DELAY EXECUTION
2025-09-02 21:03:48.712 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.033ms. Level: 2 → 3, Stake: 8.00
2025-09-02 21:03:48.712 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0468ms. Level: 3
2025-09-02 21:03:48.712 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:03:48.712
2025-09-02 21:03:48.712 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.065ms
2025-09-02 21:03:48.712 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.0768ms
2025-09-02 21:03:48.713 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:03:48.713
2025-09-02 21:03:48.713 -03:00 [INF] Profit Table updated for contract 293049966008: Profit=-4, ExitPrice=5965.519, ExitTime=00:11:56
2025-09-02 21:03:48.713 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:03:48.714 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049966008: 5965.521 at 00:11:54
2025-09-02 21:03:48.714 -03:00 [INF] [TIMING] ContractResult event concluído às 21:03:48.714 (duração: 1.8035ms)
2025-09-02 21:03:48.916 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 203.8127ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:48.921 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 208.0249ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:48.925 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 212.0499ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:48.930 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.7021ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:48.931 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 218.4681ms às 21:03:48.931
2025-09-02 21:03:48.931 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.052ms às 21:03:48.931
2025-09-02 21:03:48.931 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 218.5201ms - TRUE INSTANT execution
2025-09-02 21:03:48.937 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 224.106ms. Stake: 64.00, ProposalId: 23e86feb-db0c-96d4-acfd-e389055f2f00
2025-09-02 21:03:48.937 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 224.2628ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:03:48.937 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 3
2025-09-02 21:03:49.171 -03:00 [INF] Buy response processed: Contract 293049971388, Type: Unknown, Stake: 8
2025-09-02 21:03:49.171 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293049971388, Type: Unknown, PurchaseTime: 00:11:58
2025-09-02 21:03:50.712 -03:00 [INF] [DEBUG] entry_tick for 293049971388: epoch=1756858320 -> 00:12:00.000, price=5965.485
2025-09-02 21:03:50.712 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049971388: 5965.485 at 00:12:00
2025-09-02 21:03:50.731 -03:00 [INF] [DEBUG] entry_tick for 293049971388: epoch=1756858320 -> 00:12:00.000, price=5965.485
2025-09-02 21:03:50.731 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049971388: 5965.485 at 00:12:00
2025-09-02 21:03:52.653 -03:00 [INF] [DEBUG] entry_tick for 293049971388: epoch=1756858320 -> 00:12:00.000, price=5965.485
2025-09-02 21:03:52.653 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049971388: 5965.485 at 00:12:00
2025-09-02 21:03:52.653 -03:00 [INF] [DEBUG] entry_tick for 293049971388: epoch=1756858320 -> 00:12:00.000, price=5965.485
2025-09-02 21:03:52.653 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049971388: 5965.485 at 00:12:00
2025-09-02 21:03:54.632 -03:00 [INF] [DEBUG] entry_tick for 293049971388: epoch=1756858320 -> 00:12:00.000, price=5965.485
2025-09-02 21:03:54.633 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049971388: 5965.485 at 00:12:00
2025-09-02 21:03:54.633 -03:00 [INF] [TIMING] FALLBACK - Contrato 293049971388 detectado como finalizado
2025-09-02 21:03:54.633 -03:00 [INF] Contract 293049971388 finished: Profit=7.63, Win=True
2025-09-02 21:03:54.633 -03:00 [INF] [DEBUG] exit_time for 293049971388: sell_time_epoch=1756858322 -> 00:12:02.000
2025-09-02 21:03:54.633 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:03:54.633
2025-09-02 21:03:54.633 -03:00 [INF] [TIMING] Contract WIN at 21:03:54.633 - calling OnContractWin
2025-09-02 21:03:54.633 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:03:54.633 -03:00 [INF] Profit Table updated for contract 293049971388: Profit=7.63, ExitPrice=5965.558, ExitTime=00:12:02
2025-09-02 21:03:54.633 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:03:54.633 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293049971388: 5965.485 at 00:12:00
2025-09-02 21:03:54.633 -03:00 [INF] [TIMING] ContractResult event concluído às 21:03:54.633 (duração: 0.289ms)
2025-09-02 21:05:20.218 -03:00 [INF] Application is shutting down...
2025-09-02 21:08:31.285 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 21:08:31.310 -03:00 [INF] Hosting environment: Production
2025-09-02 21:08:31.311 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 21:08:31.549 -03:00 [INF] Conectando à API Deriv...
2025-09-02 21:08:31.979 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 21:08:32.511 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:08:32.513 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:08:32.530 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:08:32.530 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:08:32.789 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:08:32.790 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:08:32.791 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:08:32.791 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:08:32.791 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:08:32.791 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:08:32.791 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:08:32.791 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:08:32.791 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:08:32.794 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:08:32.814 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:08:32.815 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:08:46.062 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:08:46.064 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:08:46.071 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 21:08:48.512 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:08:48.512 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:08:53.487 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 21:08:53.489 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:08:53.489
2025-09-02 21:08:53.490 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:08:53.696 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:53.705 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:53.705 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:53.705 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:53.705 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:53.706 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 216.5165ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:08:53.706 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:08:53.706 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-02 21:08:55.110 -03:00 [INF] [DEBUG] Contrato comprado: 293050212708, subscrevendo para atualizações
2025-09-02 21:08:55.110 -03:00 [INF] Compra executada com sucesso. ContractId: 293050212708, TransactionId: 583717482648
2025-09-02 21:08:55.116 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:08:55.116
2025-09-02 21:08:55.116 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:08:55.116
2025-09-02 21:08:55.116 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:08:55.333 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:55.333 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:55.346 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:55.347 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:55.347 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:08:55.349 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 232.7151ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:08:55.349 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:08:55.349 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 233.5783ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:08:55.350 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-02 21:08:55.350 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:08:55.350 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:08:56.730 -03:00 [INF] [DEBUG] entry_tick for 293050212708: epoch=1756858626 -> 00:17:06.000, price=5962.841
2025-09-02 21:08:56.732 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050212708: 5962.841 at 00:17:06
2025-09-02 21:08:56.753 -03:00 [INF] [DEBUG] entry_tick for 293050212708: epoch=1756858626 -> 00:17:06.000, price=5962.841
2025-09-02 21:08:56.755 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050212708: 5962.841 at 00:17:06
2025-09-02 21:08:58.782 -03:00 [INF] [DEBUG] entry_tick for 293050212708: epoch=1756858626 -> 00:17:06.000, price=5962.841
2025-09-02 21:08:58.782 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050212708: 5962.841 at 00:17:06
2025-09-02 21:08:58.807 -03:00 [INF] [DEBUG] entry_tick for 293050212708: epoch=1756858626 -> 00:17:06.000, price=5962.841
2025-09-02 21:08:58.807 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050212708: 5962.841 at 00:17:06
2025-09-02 21:09:00.638 -03:00 [INF] [DEBUG] entry_tick for 293050212708: epoch=1756858626 -> 00:17:06.000, price=5962.841
2025-09-02 21:09:00.638 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050212708: 5962.841 at 00:17:06
2025-09-02 21:09:00.638 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050212708 detectado como finalizado
2025-09-02 21:09:00.638 -03:00 [INF] Contract 293050212708 finished: Profit=0.95, Win=True
2025-09-02 21:09:00.638 -03:00 [INF] [DEBUG] exit_time for 293050212708: sell_time_epoch=1756858629 -> 00:17:09.000
2025-09-02 21:09:00.638 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:09:00.638
2025-09-02 21:09:00.638 -03:00 [INF] [TIMING] Contract WIN at 21:09:00.638 - calling OnContractWin
2025-09-02 21:09:00.639 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:00.639 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:00.639 -03:00 [INF] Profit Table updated for contract 293050212708: Profit=0.95, ExitPrice=5962.836, ExitTime=00:17:09
2025-09-02 21:09:00.639 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050212708: 5962.841 at 00:17:06
2025-09-02 21:09:00.639 -03:00 [INF] [TIMING] ContractResult event concluído às 21:09:00.639 (duração: 1.1652ms)
2025-09-02 21:09:02.711 -03:00 [INF] [DEBUG] Contrato comprado: 293050218008, subscrevendo para atualizações
2025-09-02 21:09:02.711 -03:00 [INF] Compra executada com sucesso. ContractId: 293050218008, TransactionId: 583717493488
2025-09-02 21:09:02.711 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:09:02.711
2025-09-02 21:09:02.711 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:09:02.711
2025-09-02 21:09:02.711 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:09:02.952 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 240.7852ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:02.952 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 240.8309ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:02.952 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 240.8386ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:02.952 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 240.9239ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:02.952 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 240.9116ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:02.952 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 241.1757ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:09:02.952 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:09:02.953 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 241.4122ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:09:02.953 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:09:02.953 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:02.953 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:04.655 -03:00 [INF] [DEBUG] entry_tick for 293050218008: epoch=1756858634 -> 00:17:14.000, price=5962.663
2025-09-02 21:09:04.655 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050218008: 5962.663 at 00:17:14
2025-09-02 21:09:04.655 -03:00 [INF] [DEBUG] entry_tick for 293050218008: epoch=1756858634 -> 00:17:14.000, price=5962.663
2025-09-02 21:09:04.656 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050218008: 5962.663 at 00:17:14
2025-09-02 21:09:06.765 -03:00 [INF] [DEBUG] entry_tick for 293050218008: epoch=1756858634 -> 00:17:14.000, price=5962.663
2025-09-02 21:09:06.765 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050218008: 5962.663 at 00:17:14
2025-09-02 21:09:06.770 -03:00 [INF] [DEBUG] entry_tick for 293050218008: epoch=1756858634 -> 00:17:14.000, price=5962.663
2025-09-02 21:09:06.770 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050218008: 5962.663 at 00:17:14
2025-09-02 21:09:08.674 -03:00 [INF] [DEBUG] entry_tick for 293050218008: epoch=1756858634 -> 00:17:14.000, price=5962.663
2025-09-02 21:09:08.674 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050218008: 5962.663 at 00:17:14
2025-09-02 21:09:08.674 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050218008 detectado como finalizado
2025-09-02 21:09:08.674 -03:00 [INF] Contract 293050218008 finished: Profit=0.95, Win=True
2025-09-02 21:09:08.674 -03:00 [INF] [DEBUG] exit_time for 293050218008: sell_time_epoch=1756858636 -> 00:17:16.000
2025-09-02 21:09:08.674 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:09:08.674
2025-09-02 21:09:08.674 -03:00 [INF] [TIMING] Contract WIN at 21:09:08.674 - calling OnContractWin
2025-09-02 21:09:08.675 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:08.675 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:08.675 -03:00 [INF] Profit Table updated for contract 293050218008: Profit=0.95, ExitPrice=5962.518, ExitTime=00:17:16
2025-09-02 21:09:08.677 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050218008: 5962.663 at 00:17:14
2025-09-02 21:09:08.677 -03:00 [INF] [TIMING] ContractResult event concluído às 21:09:08.677 (duração: 2.8978ms)
2025-09-02 21:09:11.268 -03:00 [INF] [DEBUG] Contrato comprado: 293050224728, subscrevendo para atualizações
2025-09-02 21:09:11.268 -03:00 [INF] Compra executada com sucesso. ContractId: 293050224728, TransactionId: 583717506268
2025-09-02 21:09:11.268 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:09:11.268
2025-09-02 21:09:11.268 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:09:11.268
2025-09-02 21:09:11.268 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:09:11.498 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 229.1026ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:11.498 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 229.2963ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:11.503 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 234.9059ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:11.510 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 241.5708ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:11.510 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 241.7173ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:11.510 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 241.9605ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:09:11.510 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:09:11.510 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 242.1251ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:09:11.510 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:09:11.510 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:11.510 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:12.635 -03:00 [INF] [DEBUG] entry_tick for 293050224728: epoch=1756858642 -> 00:17:22.000, price=5962.981
2025-09-02 21:09:12.635 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050224728: 5962.981 at 00:17:22
2025-09-02 21:09:12.642 -03:00 [INF] [DEBUG] entry_tick for 293050224728: epoch=1756858642 -> 00:17:22.000, price=5962.981
2025-09-02 21:09:12.642 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050224728: 5962.981 at 00:17:22
2025-09-02 21:09:14.754 -03:00 [INF] [DEBUG] entry_tick for 293050224728: epoch=1756858642 -> 00:17:22.000, price=5962.981
2025-09-02 21:09:14.754 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050224728: 5962.981 at 00:17:22
2025-09-02 21:09:14.754 -03:00 [INF] [DEBUG] entry_tick for 293050224728: epoch=1756858642 -> 00:17:22.000, price=5962.981
2025-09-02 21:09:14.755 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050224728: 5962.981 at 00:17:22
2025-09-02 21:09:16.659 -03:00 [INF] [DEBUG] entry_tick for 293050224728: epoch=1756858642 -> 00:17:22.000, price=5962.981
2025-09-02 21:09:16.659 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050224728: 5962.981 at 00:17:22
2025-09-02 21:09:16.659 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050224728 detectado como finalizado
2025-09-02 21:09:16.659 -03:00 [INF] Contract 293050224728 finished: Profit=0.95, Win=True
2025-09-02 21:09:16.659 -03:00 [INF] [DEBUG] exit_time for 293050224728: sell_time_epoch=1756858645 -> 00:17:25.000
2025-09-02 21:09:16.659 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:09:16.659
2025-09-02 21:09:16.660 -03:00 [INF] [TIMING] Contract WIN at 21:09:16.660 - calling OnContractWin
2025-09-02 21:09:16.660 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:16.660 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:16.660 -03:00 [INF] Profit Table updated for contract 293050224728: Profit=0.95, ExitPrice=5962.966, ExitTime=00:17:25
2025-09-02 21:09:16.660 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050224728: 5962.981 at 00:17:22
2025-09-02 21:09:16.660 -03:00 [INF] [TIMING] ContractResult event concluído às 21:09:16.660 (duração: 0.6656ms)
2025-09-02 21:09:21.453 -03:00 [INF] [DEBUG] Contrato comprado: 293050234368, subscrevendo para atualizações
2025-09-02 21:09:21.454 -03:00 [INF] Compra executada com sucesso. ContractId: 293050234368, TransactionId: 583717521088
2025-09-02 21:09:21.454 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:09:21.454
2025-09-02 21:09:21.454 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:09:21.454
2025-09-02 21:09:21.454 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:09:21.665 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 211.0453ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:21.674 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 219.6976ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:21.674 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 219.8047ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:21.674 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 219.8112ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:21.679 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 224.8911ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:21.680 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 225.6745ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:09:21.680 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:09:21.680 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 225.7979ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:09:21.680 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:09:21.680 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:21.680 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:22.758 -03:00 [INF] [DEBUG] entry_tick for 293050234368: epoch=1756858652 -> 00:17:32.000, price=5962.739
2025-09-02 21:09:22.758 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050234368: 5962.739 at 00:17:32
2025-09-02 21:09:22.766 -03:00 [INF] [DEBUG] entry_tick for 293050234368: epoch=1756858652 -> 00:17:32.000, price=5962.739
2025-09-02 21:09:22.766 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050234368: 5962.739 at 00:17:32
2025-09-02 21:09:24.670 -03:00 [INF] [DEBUG] entry_tick for 293050234368: epoch=1756858652 -> 00:17:32.000, price=5962.739
2025-09-02 21:09:24.671 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050234368: 5962.739 at 00:17:32
2025-09-02 21:09:24.672 -03:00 [INF] [DEBUG] entry_tick for 293050234368: epoch=1756858652 -> 00:17:32.000, price=5962.739
2025-09-02 21:09:24.673 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050234368: 5962.739 at 00:17:32
2025-09-02 21:09:26.649 -03:00 [INF] [DEBUG] entry_tick for 293050234368: epoch=1756858652 -> 00:17:32.000, price=5962.739
2025-09-02 21:09:26.649 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050234368: 5962.739 at 00:17:32
2025-09-02 21:09:26.649 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050234368 detectado como finalizado
2025-09-02 21:09:26.649 -03:00 [INF] Contract 293050234368 finished: Profit=0.95, Win=True
2025-09-02 21:09:26.649 -03:00 [INF] [DEBUG] exit_time for 293050234368: sell_time_epoch=1756858654 -> 00:17:34.000
2025-09-02 21:09:26.649 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:09:26.649
2025-09-02 21:09:26.649 -03:00 [INF] [TIMING] Contract WIN at 21:09:26.649 - calling OnContractWin
2025-09-02 21:09:26.649 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:26.649 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:26.649 -03:00 [INF] Profit Table updated for contract 293050234368: Profit=0.95, ExitPrice=5962.51, ExitTime=00:17:34
2025-09-02 21:09:26.653 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050234368: 5962.739 at 00:17:32
2025-09-02 21:09:26.653 -03:00 [INF] [TIMING] ContractResult event concluído às 21:09:26.653 (duração: 3.8708ms)
2025-09-02 21:09:28.676 -03:00 [INF] [DEBUG] Contrato comprado: 293050241248, subscrevendo para atualizações
2025-09-02 21:09:28.676 -03:00 [INF] Compra executada com sucesso. ContractId: 293050241248, TransactionId: 583717532268
2025-09-02 21:09:28.676 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:09:28.676
2025-09-02 21:09:28.676 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:09:28.676
2025-09-02 21:09:28.676 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:09:28.883 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 206.8231ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:28.883 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 207.0439ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:28.894 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.9087ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:28.894 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.9089ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:28.894 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 218.0253ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:28.894 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 218.1566ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:09:28.894 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:09:28.894 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 218.2472ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:09:28.894 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:09:28.894 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:28.894 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:30.681 -03:00 [INF] [DEBUG] entry_tick for 293050241248: epoch=1756858660 -> 00:17:40.000, price=5963.011
2025-09-02 21:09:30.681 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050241248: 5963.011 at 00:17:40
2025-09-02 21:09:30.681 -03:00 [INF] [DEBUG] entry_tick for 293050241248: epoch=1756858660 -> 00:17:40.000, price=5963.011
2025-09-02 21:09:30.682 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050241248: 5963.011 at 00:17:40
2025-09-02 21:09:32.744 -03:00 [INF] [DEBUG] entry_tick for 293050241248: epoch=1756858660 -> 00:17:40.000, price=5963.011
2025-09-02 21:09:32.744 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050241248: 5963.011 at 00:17:40
2025-09-02 21:09:32.750 -03:00 [INF] [DEBUG] entry_tick for 293050241248: epoch=1756858660 -> 00:17:40.000, price=5963.011
2025-09-02 21:09:32.750 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050241248: 5963.011 at 00:17:40
2025-09-02 21:09:34.652 -03:00 [INF] [DEBUG] entry_tick for 293050241248: epoch=1756858660 -> 00:17:40.000, price=5963.011
2025-09-02 21:09:34.652 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050241248: 5963.011 at 00:17:40
2025-09-02 21:09:34.652 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050241248 detectado como finalizado
2025-09-02 21:09:34.652 -03:00 [INF] Contract 293050241248 finished: Profit=-1, Win=False
2025-09-02 21:09:34.653 -03:00 [INF] [DEBUG] exit_time for 293050241248: sell_time_epoch=1756858663 -> 00:17:43.000
2025-09-02 21:09:34.653 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:09:34.653
2025-09-02 21:09:34.653 -03:00 [INF] [TIMING] Contract LOSS at 21:09:34.653 - ZERO-DELAY EXECUTION
2025-09-02 21:09:34.654 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0444ms. Level: 0 → 1, Stake: 2.00
2025-09-02 21:09:34.654 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.105ms. Level: 1
2025-09-02 21:09:34.654 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:09:34.654
2025-09-02 21:09:34.654 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.3559ms
2025-09-02 21:09:34.654 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.4968ms
2025-09-02 21:09:34.654 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:09:34.654
2025-09-02 21:09:34.654 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:09:34.655 -03:00 [INF] Profit Table updated for contract 293050241248: Profit=-1, ExitPrice=5963.085, ExitTime=00:17:43
2025-09-02 21:09:34.655 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050241248: 5963.011 at 00:17:40
2025-09-02 21:09:34.655 -03:00 [INF] [TIMING] ContractResult event concluído às 21:09:34.655 (duração: 2.4489ms)
2025-09-02 21:09:34.875 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 220.8331ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:34.875 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 220.8721ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:34.875 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 220.905ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:34.875 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 221.3781ms às 21:09:34.875
2025-09-02 21:09:34.875 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0422ms às 21:09:34.875
2025-09-02 21:09:34.875 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 221.4203ms - TRUE INSTANT execution
2025-09-02 21:09:34.880 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.7751ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:34.880 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.8385ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:34.880 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 225.9405ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:09:34.880 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-02 21:09:35.105 -03:00 [INF] Buy response processed: Contract 293050247268, Type: Unknown, Stake: 2
2025-09-02 21:09:35.106 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293050247268, Type: Unknown, PurchaseTime: 00:17:44
2025-09-02 21:09:36.766 -03:00 [INF] [DEBUG] entry_tick for 293050247268: epoch=1756858666 -> 00:17:46.000, price=5963.261
2025-09-02 21:09:36.766 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050247268: 5963.261 at 00:17:46
2025-09-02 21:09:36.766 -03:00 [INF] [DEBUG] entry_tick for 293050247268: epoch=1756858666 -> 00:17:46.000, price=5963.261
2025-09-02 21:09:36.767 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050247268: 5963.261 at 00:17:46
2025-09-02 21:09:38.744 -03:00 [INF] [DEBUG] entry_tick for 293050247268: epoch=1756858666 -> 00:17:46.000, price=5963.261
2025-09-02 21:09:38.744 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050247268: 5963.261 at 00:17:46
2025-09-02 21:09:38.752 -03:00 [INF] [DEBUG] entry_tick for 293050247268: epoch=1756858666 -> 00:17:46.000, price=5963.261
2025-09-02 21:09:38.752 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050247268: 5963.261 at 00:17:46
2025-09-02 21:09:40.630 -03:00 [INF] [DEBUG] entry_tick for 293050247268: epoch=1756858666 -> 00:17:46.000, price=5963.261
2025-09-02 21:09:40.630 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050247268: 5963.261 at 00:17:46
2025-09-02 21:09:40.630 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050247268 detectado como finalizado
2025-09-02 21:09:40.630 -03:00 [INF] Contract 293050247268 finished: Profit=-2, Win=False
2025-09-02 21:09:40.630 -03:00 [INF] [DEBUG] exit_time for 293050247268: sell_time_epoch=1756858668 -> 00:17:48.000
2025-09-02 21:09:40.630 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:09:40.630
2025-09-02 21:09:40.630 -03:00 [INF] [TIMING] Contract LOSS at 21:09:40.630 - ZERO-DELAY EXECUTION
2025-09-02 21:09:40.631 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0267ms. Level: 1 → 2, Stake: 4.00
2025-09-02 21:09:40.631 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0385ms. Level: 2
2025-09-02 21:09:40.631 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:09:40.631
2025-09-02 21:09:40.631 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1822ms
2025-09-02 21:09:40.631 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1928ms
2025-09-02 21:09:40.631 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:09:40.631
2025-09-02 21:09:40.631 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:09:40.631 -03:00 [INF] Profit Table updated for contract 293050247268: Profit=-2, ExitPrice=5963.386, ExitTime=00:17:48
2025-09-02 21:09:40.632 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050247268: 5963.261 at 00:17:46
2025-09-02 21:09:40.632 -03:00 [INF] [TIMING] ContractResult event concluído às 21:09:40.632 (duração: 2.022ms)
2025-09-02 21:09:40.827 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 195.8759ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:40.827 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 196.0076ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:40.848 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.1712ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:40.848 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.2216ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:40.848 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 217.397ms às 21:09:40.848
2025-09-02 21:09:40.848 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0289ms às 21:09:40.848
2025-09-02 21:09:40.848 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 217.4259ms - TRUE INSTANT execution
2025-09-02 21:09:40.848 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.3239ms. Stake: 64.00, ProposalId: 19feafcb-1e7c-323a-d2d8-2ab5ab4423da
2025-09-02 21:09:40.848 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 217.3883ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:09:40.848 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-09-02 21:09:41.101 -03:00 [INF] Buy response processed: Contract 293050252508, Type: Unknown, Stake: 4
2025-09-02 21:09:41.101 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293050252508, Type: Unknown, PurchaseTime: 00:17:50
2025-09-02 21:09:42.729 -03:00 [INF] [DEBUG] entry_tick for 293050252508: epoch=1756858672 -> 00:17:52.000, price=5963.531
2025-09-02 21:09:42.729 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050252508: 5963.531 at 00:17:52
2025-09-02 21:09:42.736 -03:00 [INF] [DEBUG] entry_tick for 293050252508: epoch=1756858672 -> 00:17:52.000, price=5963.531
2025-09-02 21:09:42.737 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050252508: 5963.531 at 00:17:52
2025-09-02 21:09:44.724 -03:00 [INF] [DEBUG] entry_tick for 293050252508: epoch=1756858672 -> 00:17:52.000, price=5963.531
2025-09-02 21:09:44.725 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050252508: 5963.531 at 00:17:52
2025-09-02 21:09:44.742 -03:00 [INF] [DEBUG] entry_tick for 293050252508: epoch=1756858672 -> 00:17:52.000, price=5963.531
2025-09-02 21:09:44.743 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050252508: 5963.531 at 00:17:52
2025-09-02 21:09:46.647 -03:00 [INF] [DEBUG] entry_tick for 293050252508: epoch=1756858672 -> 00:17:52.000, price=5963.531
2025-09-02 21:09:46.647 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050252508: 5963.531 at 00:17:52
2025-09-02 21:09:46.647 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050252508 detectado como finalizado
2025-09-02 21:09:46.647 -03:00 [INF] Contract 293050252508 finished: Profit=3.81, Win=True
2025-09-02 21:09:46.647 -03:00 [INF] [DEBUG] exit_time for 293050252508: sell_time_epoch=1756858674 -> 00:17:54.000
2025-09-02 21:09:46.647 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:09:46.647
2025-09-02 21:09:46.647 -03:00 [INF] [TIMING] Contract WIN at 21:09:46.647 - calling OnContractWin
2025-09-02 21:09:46.647 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:09:46.647 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:09:46.647 -03:00 [INF] Profit Table updated for contract 293050252508: Profit=3.81, ExitPrice=5963.487, ExitTime=00:17:54
2025-09-02 21:09:46.648 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050252508: 5963.531 at 00:17:52
2025-09-02 21:09:46.649 -03:00 [INF] [TIMING] ContractResult event concluído às 21:09:46.649 (duração: 1.6317ms)
2025-09-02 21:10:55.300 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:10:55.300 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:10:57.297 -03:00 [INF] [DEBUG] Contrato comprado: 293050320468, subscrevendo para atualizações
2025-09-02 21:10:57.297 -03:00 [INF] Compra executada com sucesso. ContractId: 293050320468, TransactionId: 583717679888
2025-09-02 21:10:57.297 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:10:57.297
2025-09-02 21:10:57.297 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:10:57.297
2025-09-02 21:10:57.297 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:10:57.499 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 201.7339ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:10:57.499 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 201.8421ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:10:57.506 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 193.0027ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:10:57.512 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 215.2889ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:10:57.518 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 221.024ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:10:57.518 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 221.1826ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:10:57.518 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:10:57.518 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 221.2913ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:10:57.518 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:10:57.518 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:10:57.518 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:10:58.620 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:10:58.621 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:10:58.633 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:10:58.634 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:00.669 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:11:00.669 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:00.675 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:11:00.676 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:02.722 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:11:02.723 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:02.731 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:11:02.732 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:04.640 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:11:04.643 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:04.646 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:11:04.646 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:06.625 -03:00 [INF] [DEBUG] entry_tick for 293050320468: epoch=1756858748 -> 00:19:08.000, price=5963.898
2025-09-02 21:11:06.625 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:06.625 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050320468 detectado como finalizado
2025-09-02 21:11:06.625 -03:00 [INF] Contract 293050320468 finished: Profit=-1, Win=False
2025-09-02 21:11:06.625 -03:00 [INF] [DEBUG] exit_time for 293050320468: sell_time_epoch=1756858754 -> 00:19:14.000
2025-09-02 21:11:06.625 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:11:06.625
2025-09-02 21:11:06.625 -03:00 [INF] [TIMING] Contract LOSS at 21:11:06.625 - ZERO-DELAY EXECUTION
2025-09-02 21:11:06.625 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0252ms. Level: 0 → 1, Stake: 2.00
2025-09-02 21:11:06.625 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0372ms. Level: 1
2025-09-02 21:11:06.625 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:11:06.625
2025-09-02 21:11:06.625 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.0562ms
2025-09-02 21:11:06.625 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.0684ms
2025-09-02 21:11:06.625 -03:00 [INF] Profit Table updated for contract 293050320468: Profit=-1, ExitPrice=5964.172, ExitTime=00:19:14
2025-09-02 21:11:06.625 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:11:06.625
2025-09-02 21:11:06.625 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:11:06.627 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050320468: 5963.898 at 00:19:08
2025-09-02 21:11:06.627 -03:00 [INF] [TIMING] ContractResult event concluído às 21:11:06.627 (duração: 1.9871ms)
2025-09-02 21:11:06.854 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 229.0321ms às 21:11:06.854
2025-09-02 21:11:06.854 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0806ms às 21:11:06.854
2025-09-02 21:11:06.854 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 229.1127ms - TRUE INSTANT execution
2025-09-02 21:11:06.864 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 238.2863ms. Stake: 4.00, ProposalId: 0f4ac78b-ec2e-dd51-5307-e35f1585a1aa
2025-09-02 21:11:06.874 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 244.4266ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:06.874 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 244.4722ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:06.883 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 252.8954ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:06.888 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 258.3984ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:06.888 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 262.8084ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:11:06.888 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-02 21:11:07.094 -03:00 [INF] Buy response processed: Contract 293050329068, Type: Unknown, Stake: 2
2025-09-02 21:11:07.094 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293050329068, Type: Unknown, PurchaseTime: 00:19:16
2025-09-02 21:11:08.635 -03:00 [INF] [DEBUG] entry_tick for 293050329068: epoch=1756858758 -> 00:19:18.000, price=5963.87
2025-09-02 21:11:08.636 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050329068: 5963.87 at 00:19:18
2025-09-02 21:11:08.636 -03:00 [INF] [DEBUG] entry_tick for 293050329068: epoch=1756858758 -> 00:19:18.000, price=5963.87
2025-09-02 21:11:08.637 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050329068: 5963.87 at 00:19:18
2025-09-02 21:11:10.615 -03:00 [INF] [DEBUG] entry_tick for 293050329068: epoch=1756858758 -> 00:19:18.000, price=5963.87
2025-09-02 21:11:10.616 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050329068: 5963.87 at 00:19:18
2025-09-02 21:11:10.623 -03:00 [INF] [DEBUG] entry_tick for 293050329068: epoch=1756858758 -> 00:19:18.000, price=5963.87
2025-09-02 21:11:10.623 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050329068: 5963.87 at 00:19:18
2025-09-02 21:11:12.749 -03:00 [INF] [DEBUG] entry_tick for 293050329068: epoch=1756858758 -> 00:19:18.000, price=5963.87
2025-09-02 21:11:12.749 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050329068: 5963.87 at 00:19:18
2025-09-02 21:11:12.770 -03:00 [INF] [DEBUG] entry_tick for 293050329068: epoch=1756858758 -> 00:19:18.000, price=5963.87
2025-09-02 21:11:12.770 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050329068: 5963.87 at 00:19:18
2025-09-02 21:11:14.651 -03:00 [INF] [DEBUG] entry_tick for 293050329068: epoch=1756858758 -> 00:19:18.000, price=5963.87
2025-09-02 21:11:14.651 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050329068: 5963.87 at 00:19:18
2025-09-02 21:11:14.651 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050329068 detectado como finalizado
2025-09-02 21:11:14.651 -03:00 [INF] Contract 293050329068 finished: Profit=-2, Win=False
2025-09-02 21:11:14.651 -03:00 [INF] [DEBUG] exit_time for 293050329068: sell_time_epoch=1756858764 -> 00:19:24.000
2025-09-02 21:11:14.651 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:11:14.651
2025-09-02 21:11:14.651 -03:00 [INF] [TIMING] Contract LOSS at 21:11:14.651 - ZERO-DELAY EXECUTION
2025-09-02 21:11:14.651 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.022ms. Level: 1 → 2, Stake: 4.00
2025-09-02 21:11:14.652 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.056ms. Level: 2
2025-09-02 21:11:14.652 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:11:14.652
2025-09-02 21:11:14.652 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1168ms
2025-09-02 21:11:14.652 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1373ms
2025-09-02 21:11:14.652 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:11:14.652
2025-09-02 21:11:14.652 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:11:14.652 -03:00 [INF] Profit Table updated for contract 293050329068: Profit=-2, ExitPrice=5964.527, ExitTime=00:19:24
2025-09-02 21:11:14.653 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050329068: 5963.87 at 00:19:18
2025-09-02 21:11:14.653 -03:00 [INF] [TIMING] ContractResult event concluído às 21:11:14.653 (duração: 1.9371ms)
2025-09-02 21:11:15.177 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 525.6596ms às 21:11:15.177
2025-09-02 21:11:15.177 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0719ms às 21:11:15.177
2025-09-02 21:11:15.177 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 525.7315ms - TRUE INSTANT execution
2025-09-02 21:11:15.184 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 532.692ms. Stake: 8.00, ProposalId: 1175de89-b976-aa7e-840a-818298d3697b
2025-09-02 21:11:15.188 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 533.2831ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:15.216 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 564.1454ms. Stake: 4.00, ProposalId: 0f4ac78b-ec2e-dd51-5307-e35f1585a1aa
2025-09-02 21:11:15.216 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 564.1985ms. Stake: 8.00, ProposalId: 1175de89-b976-aa7e-840a-818298d3697b
2025-09-02 21:11:15.216 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 561.6557ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:15.216 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 564.3088ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:11:15.216 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-09-02 21:11:15.436 -03:00 [INF] Buy response processed: Contract 293050336388, Type: Unknown, Stake: 4
2025-09-02 21:11:15.437 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293050336388, Type: Unknown, PurchaseTime: 00:19:25
2025-09-02 21:11:16.657 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:16.657 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:16.680 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:16.680 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:18.655 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:18.655 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:18.660 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:18.660 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:20.734 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:20.734 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:20.737 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:20.737 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:22.651 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:22.652 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:22.668 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:22.668 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:24.734 -03:00 [INF] [DEBUG] entry_tick for 293050336388: epoch=1756858766 -> 00:19:26.000, price=5964.796
2025-09-02 21:11:24.734 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:24.734 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050336388 detectado como finalizado
2025-09-02 21:11:24.734 -03:00 [INF] Contract 293050336388 finished: Profit=-4, Win=False
2025-09-02 21:11:24.734 -03:00 [INF] [DEBUG] exit_time for 293050336388: sell_time_epoch=1756858772 -> 00:19:32.000
2025-09-02 21:11:24.734 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:11:24.734
2025-09-02 21:11:24.734 -03:00 [INF] [TIMING] Contract LOSS at 21:11:24.734 - ZERO-DELAY EXECUTION
2025-09-02 21:11:24.734 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0383ms. Level: 2 → 3, Stake: 8.00
2025-09-02 21:11:24.734 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0488ms. Level: 3
2025-09-02 21:11:24.734 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:11:24.734
2025-09-02 21:11:24.734 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.0694ms
2025-09-02 21:11:24.734 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.0783ms
2025-09-02 21:11:24.734 -03:00 [INF] Profit Table updated for contract 293050336388: Profit=-4, ExitPrice=5965.132, ExitTime=00:19:32
2025-09-02 21:11:24.735 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050336388: 5964.796 at 00:19:26
2025-09-02 21:11:24.736 -03:00 [INF] [TIMING] ContractResult event concluído às 21:11:24.736 (duração: 1.4849ms)
2025-09-02 21:11:24.736 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:11:24.736
2025-09-02 21:11:24.736 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:11:24.935 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 199.3313ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:24.935 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 199.5043ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:24.937 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 201.2611ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:24.937 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 201.5746ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:24.937 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 203.257ms às 21:11:24.937
2025-09-02 21:11:24.937 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.057ms às 21:11:24.937
2025-09-02 21:11:24.937 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 203.314ms - TRUE INSTANT execution
2025-09-02 21:11:24.950 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 214.4558ms. Stake: 64.00, ProposalId: f5773a42-d50f-d812-6f32-8818ddfbcc5d
2025-09-02 21:11:24.950 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 214.8007ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:11:24.950 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 3
2025-09-02 21:11:25.170 -03:00 [INF] Buy response processed: Contract 293050345208, Type: Unknown, Stake: 8
2025-09-02 21:11:25.171 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293050345208, Type: Unknown, PurchaseTime: 00:19:34
2025-09-02 21:11:26.655 -03:00 [INF] [DEBUG] entry_tick for 293050345208: epoch=1756858776 -> 00:19:36.000, price=5964.876
2025-09-02 21:11:26.655 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050345208: 5964.876 at 00:19:36
2025-09-02 21:11:26.667 -03:00 [INF] [DEBUG] entry_tick for 293050345208: epoch=1756858776 -> 00:19:36.000, price=5964.876
2025-09-02 21:11:26.667 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050345208: 5964.876 at 00:19:36
2025-09-02 21:11:28.654 -03:00 [INF] [DEBUG] entry_tick for 293050345208: epoch=1756858776 -> 00:19:36.000, price=5964.876
2025-09-02 21:11:28.654 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050345208: 5964.876 at 00:19:36
2025-09-02 21:11:28.663 -03:00 [INF] [DEBUG] entry_tick for 293050345208: epoch=1756858776 -> 00:19:36.000, price=5964.876
2025-09-02 21:11:28.663 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050345208: 5964.876 at 00:19:36
2025-09-02 21:11:30.618 -03:00 [INF] [DEBUG] entry_tick for 293050345208: epoch=1756858776 -> 00:19:36.000, price=5964.876
2025-09-02 21:11:30.618 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050345208: 5964.876 at 00:19:36
2025-09-02 21:11:30.631 -03:00 [INF] [DEBUG] entry_tick for 293050345208: epoch=1756858776 -> 00:19:36.000, price=5964.876
2025-09-02 21:11:30.631 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050345208: 5964.876 at 00:19:36
2025-09-02 21:11:32.716 -03:00 [INF] [DEBUG] entry_tick for 293050345208: epoch=1756858776 -> 00:19:36.000, price=5964.876
2025-09-02 21:11:32.716 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050345208: 5964.876 at 00:19:36
2025-09-02 21:11:32.717 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050345208 detectado como finalizado
2025-09-02 21:11:32.717 -03:00 [INF] Contract 293050345208 finished: Profit=-8, Win=False
2025-09-02 21:11:32.717 -03:00 [INF] [DEBUG] exit_time for 293050345208: sell_time_epoch=1756858782 -> 00:19:42.000
2025-09-02 21:11:32.717 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:11:32.717
2025-09-02 21:11:32.717 -03:00 [INF] [TIMING] Contract LOSS at 21:11:32.717 - ZERO-DELAY EXECUTION
2025-09-02 21:11:32.717 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:11:32.717 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:11:32.717 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1287ms
2025-09-02 21:11:32.717 -03:00 [INF] Profit Table updated for contract 293050345208: Profit=-8, ExitPrice=5965.05, ExitTime=00:19:42
2025-09-02 21:11:32.720 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050345208: 5964.876 at 00:19:36
2025-09-02 21:11:32.720 -03:00 [INF] [TIMING] ContractResult event concluído às 21:11:32.720 (duração: 3.8026ms)
2025-09-02 21:12:05.240 -03:00 [INF] Application is shutting down...
2025-09-02 21:18:28.902 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 21:18:29.609 -03:00 [INF] Hosting environment: Production
2025-09-02 21:18:29.609 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 21:18:29.922 -03:00 [INF] Conectando à API Deriv...
2025-09-02 21:18:31.158 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 21:18:32.180 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:18:32.183 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:18:32.201 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:18:32.201 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:18:32.832 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:18:32.833 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:18:32.834 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:18:32.834 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:18:32.834 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:18:32.834 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:18:32.835 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:18:32.835 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:18:32.835 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:18:32.855 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:18:32.945 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:18:32.945 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:18:32.946 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:18:32.946 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:18:32.946 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:18:32.946 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:18:32.946 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:18:32.946 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:18:32.946 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:18:32.946 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:18:50.831 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:18:50.833 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:18:50.841 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 21:18:53.265 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:18:53.266 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:18:57.711 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:18:57.711 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:19:01.297 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 21:19:01.298 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:19:01.298
2025-09-02 21:19:01.298 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:19:01.493 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:01.501 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:01.505 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:01.505 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:01.513 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:01.514 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 215.0737ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:19:01.514 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:19:01.514 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-02 21:19:02.798 -03:00 [INF] [DEBUG] Contrato comprado: 293050721248, subscrevendo para atualizações
2025-09-02 21:19:02.799 -03:00 [INF] Compra executada com sucesso. ContractId: 293050721248, TransactionId: 583718460168
2025-09-02 21:19:02.805 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:19:02.805
2025-09-02 21:19:02.805 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:19:02.805
2025-09-02 21:19:02.805 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:19:02.999 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:02.999 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:03.012 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:03.017 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:03.027 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:19:03.027 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 222.3301ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:19:03.027 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:19:03.028 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 222.8426ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:19:03.028 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-02 21:19:03.028 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:19:03.028 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:19:04.592 -03:00 [INF] [DEBUG] entry_tick for 293050721248: epoch=1756859234 -> 00:27:14.000, price=5964.632
2025-09-02 21:19:04.594 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050721248: 5964.632 at 00:27:14
2025-09-02 21:19:04.594 -03:00 [INF] [DEBUG] entry_tick for 293050721248: epoch=1756859234 -> 00:27:14.000, price=5964.632
2025-09-02 21:19:04.595 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050721248: 5964.632 at 00:27:14
2025-09-02 21:19:06.667 -03:00 [INF] [DEBUG] entry_tick for 293050721248: epoch=1756859234 -> 00:27:14.000, price=5964.632
2025-09-02 21:19:06.667 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050721248: 5964.632 at 00:27:14
2025-09-02 21:19:06.686 -03:00 [INF] [DEBUG] entry_tick for 293050721248: epoch=1756859234 -> 00:27:14.000, price=5964.632
2025-09-02 21:19:06.686 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050721248: 5964.632 at 00:27:14
2025-09-02 21:19:08.646 -03:00 [INF] [DEBUG] entry_tick for 293050721248: epoch=1756859234 -> 00:27:14.000, price=5964.632
2025-09-02 21:19:08.647 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050721248: 5964.632 at 00:27:14
2025-09-02 21:19:08.647 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050721248 detectado como finalizado
2025-09-02 21:19:08.647 -03:00 [INF] Contract 293050721248 finished: Profit=0.95, Win=True
2025-09-02 21:19:08.647 -03:00 [INF] [DEBUG] exit_time for 293050721248: sell_time_epoch=1756859236 -> 00:27:16.000
2025-09-02 21:19:08.647 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:19:08.647
2025-09-02 21:19:08.647 -03:00 [INF] [TIMING] Contract WIN at 21:19:08.647 - calling OnContractWin
2025-09-02 21:19:08.648 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:19:08.648 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:19:08.648 -03:00 [INF] Profit Table updated for contract 293050721248: Profit=0.95, ExitPrice=5964.799, ExitTime=00:27:16
2025-09-02 21:19:08.650 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050721248: 5964.632 at 00:27:14
2025-09-02 21:19:08.650 -03:00 [INF] [TIMING] ContractResult event concluído às 21:19:08.650 (duração: 3.1979ms)
2025-09-02 21:19:16.306 -03:00 [INF] [DEBUG] Contrato comprado: 293050732428, subscrevendo para atualizações
2025-09-02 21:19:16.306 -03:00 [INF] Compra executada com sucesso. ContractId: 293050732428, TransactionId: 583718481668
2025-09-02 21:19:16.306 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:19:16.306
2025-09-02 21:19:16.306 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:19:16.306
2025-09-02 21:19:16.306 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:19:16.558 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 231.6753ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:16.558 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 251.8867ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:16.565 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 258.5931ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:16.568 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 261.7348ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:16.568 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 261.844ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:16.568 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 262.12ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:19:16.568 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:19:16.569 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 262.2759ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:19:16.569 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:19:16.569 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:19:16.569 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:19:18.666 -03:00 [INF] [DEBUG] entry_tick for 293050732428: epoch=1756859248 -> 00:27:28.000, price=5965.273
2025-09-02 21:19:18.666 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050732428: 5965.273 at 00:27:28
2025-09-02 21:19:18.672 -03:00 [INF] [DEBUG] entry_tick for 293050732428: epoch=1756859248 -> 00:27:28.000, price=5965.273
2025-09-02 21:19:18.672 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050732428: 5965.273 at 00:27:28
2025-09-02 21:19:20.608 -03:00 [INF] [DEBUG] entry_tick for 293050732428: epoch=1756859248 -> 00:27:28.000, price=5965.273
2025-09-02 21:19:20.608 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050732428: 5965.273 at 00:27:28
2025-09-02 21:19:20.608 -03:00 [INF] [DEBUG] entry_tick for 293050732428: epoch=1756859248 -> 00:27:28.000, price=5965.273
2025-09-02 21:19:20.608 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050732428: 5965.273 at 00:27:28
2025-09-02 21:19:22.593 -03:00 [INF] [DEBUG] entry_tick for 293050732428: epoch=1756859248 -> 00:27:28.000, price=5965.273
2025-09-02 21:19:22.594 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050732428: 5965.273 at 00:27:28
2025-09-02 21:19:22.594 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050732428 detectado como finalizado
2025-09-02 21:19:22.594 -03:00 [INF] Contract 293050732428 finished: Profit=0.95, Win=True
2025-09-02 21:19:22.594 -03:00 [INF] [DEBUG] exit_time for 293050732428: sell_time_epoch=1756859251 -> 00:27:31.000
2025-09-02 21:19:22.594 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:19:22.594
2025-09-02 21:19:22.594 -03:00 [INF] [TIMING] Contract WIN at 21:19:22.594 - calling OnContractWin
2025-09-02 21:19:22.594 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:19:22.594 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:19:22.594 -03:00 [INF] Profit Table updated for contract 293050732428: Profit=0.95, ExitPrice=5965.357, ExitTime=00:27:31
2025-09-02 21:19:22.594 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050732428: 5965.273 at 00:27:28
2025-09-02 21:19:22.595 -03:00 [INF] [TIMING] ContractResult event concluído às 21:19:22.595 (duração: 0.6714ms)
2025-09-02 21:19:27.040 -03:00 [INF] [DEBUG] Contrato comprado: 293050741988, subscrevendo para atualizações
2025-09-02 21:19:27.041 -03:00 [INF] Compra executada com sucesso. ContractId: 293050741988, TransactionId: 583718499928
2025-09-02 21:19:27.041 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:19:27.041
2025-09-02 21:19:27.041 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:19:27.041
2025-09-02 21:19:27.041 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:19:27.279 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 238.3525ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:27.279 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 238.4348ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:27.279 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 238.5089ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:27.288 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 247.4952ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:27.301 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 260.1981ms. Stake: 64.00, ProposalId: 014fc892-0196-e600-262e-1131655e6e73
2025-09-02 21:19:27.301 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 260.3857ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:19:27.301 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:19:27.301 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 260.4813ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:19:27.301 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:19:27.301 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:19:27.301 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:19:28.655 -03:00 [INF] [DEBUG] entry_tick for 293050741988: epoch=1756859258 -> 00:27:38.000, price=5965.833
2025-09-02 21:19:28.656 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050741988: 5965.833 at 00:27:38
2025-09-02 21:19:28.684 -03:00 [INF] [DEBUG] entry_tick for 293050741988: epoch=1756859258 -> 00:27:38.000, price=5965.833
2025-09-02 21:19:28.684 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050741988: 5965.833 at 00:27:38
2025-09-02 21:19:30.579 -03:00 [INF] [DEBUG] entry_tick for 293050741988: epoch=1756859258 -> 00:27:38.000, price=5965.833
2025-09-02 21:19:30.579 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050741988: 5965.833 at 00:27:38
2025-09-02 21:19:30.605 -03:00 [INF] [DEBUG] entry_tick for 293050741988: epoch=1756859258 -> 00:27:38.000, price=5965.833
2025-09-02 21:19:30.605 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050741988: 5965.833 at 00:27:38
2025-09-02 21:19:32.644 -03:00 [INF] [DEBUG] entry_tick for 293050741988: epoch=1756859258 -> 00:27:38.000, price=5965.833
2025-09-02 21:19:32.644 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050741988: 5965.833 at 00:27:38
2025-09-02 21:19:32.644 -03:00 [INF] [TIMING] FALLBACK - Contrato 293050741988 detectado como finalizado
2025-09-02 21:19:32.644 -03:00 [INF] Contract 293050741988 finished: Profit=0.95, Win=True
2025-09-02 21:19:32.644 -03:00 [INF] [DEBUG] exit_time for 293050741988: sell_time_epoch=1756859260 -> 00:27:40.000
2025-09-02 21:19:32.644 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:19:32.644
2025-09-02 21:19:32.644 -03:00 [INF] [TIMING] Contract WIN at 21:19:32.644 - calling OnContractWin
2025-09-02 21:19:32.644 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:19:32.644 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:19:32.644 -03:00 [INF] Profit Table updated for contract 293050741988: Profit=0.95, ExitPrice=5966.003, ExitTime=00:27:40
2025-09-02 21:19:32.645 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293050741988: 5965.833 at 00:27:38
2025-09-02 21:19:32.645 -03:00 [INF] [TIMING] ContractResult event concluído às 21:19:32.645 (duração: 1.2986ms)
2025-09-02 21:23:00.800 -03:00 [INF] Application is shutting down...
2025-09-02 21:25:40.145 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 21:25:40.173 -03:00 [INF] Hosting environment: Production
2025-09-02 21:25:40.174 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 21:25:40.501 -03:00 [INF] Conectando à API Deriv...
2025-09-02 21:25:41.211 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 21:25:41.614 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:25:41.617 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:25:41.762 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:25:41.762 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:25:42.091 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:25:42.092 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:25:42.092 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:25:42.092 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:25:42.092 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:25:42.092 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:25:42.092 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:25:42.092 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:25:42.092 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:25:42.094 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:25:42.209 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:26:04.968 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:26:04.970 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:26:04.979 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 21:26:07.636 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:26:07.636 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:26:17.376 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 21:26:17.378 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:26:17.378
2025-09-02 21:26:17.378 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:26:17.597 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:17.606 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:17.606 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:17.606 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:17.606 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:17.607 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 228.8052ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:26:17.607 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:26:17.607 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-02 21:26:19.021 -03:00 [INF] [DEBUG] Contrato comprado: 293051094048, subscrevendo para atualizações
2025-09-02 21:26:19.022 -03:00 [INF] Compra executada com sucesso. ContractId: 293051094048, TransactionId: 583719187928
2025-09-02 21:26:19.028 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:26:19.028
2025-09-02 21:26:19.028 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:26:19.028
2025-09-02 21:26:19.028 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:26:19.226 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:19.229 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:19.237 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:19.238 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:19.238 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:26:19.238 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 210.0149ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:26:19.238 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:26:19.238 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 210.2553ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:26:19.238 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-02 21:26:19.238 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:26:19.238 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:26:20.497 -03:00 [INF] [DEBUG] entry_tick for 293051094048: epoch=1756859670 -> 00:34:30.000, price=5964.512
2025-09-02 21:26:20.499 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051094048: 5964.512 at 00:34:30
2025-09-02 21:26:20.499 -03:00 [INF] [DEBUG] entry_tick for 293051094048: epoch=1756859670 -> 00:34:30.000, price=5964.512
2025-09-02 21:26:20.500 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051094048: 5964.512 at 00:34:30
2025-09-02 21:26:22.518 -03:00 [INF] [DEBUG] entry_tick for 293051094048: epoch=1756859670 -> 00:34:30.000, price=5964.512
2025-09-02 21:26:22.518 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051094048: 5964.512 at 00:34:30
2025-09-02 21:26:22.521 -03:00 [INF] [DEBUG] entry_tick for 293051094048: epoch=1756859670 -> 00:34:30.000, price=5964.512
2025-09-02 21:26:22.521 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051094048: 5964.512 at 00:34:30
2025-09-02 21:26:24.478 -03:00 [INF] [DEBUG] entry_tick for 293051094048: epoch=1756859670 -> 00:34:30.000, price=5964.512
2025-09-02 21:26:24.479 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051094048: 5964.512 at 00:34:30
2025-09-02 21:26:24.479 -03:00 [INF] [TIMING] FALLBACK - Contrato 293051094048 detectado como finalizado
2025-09-02 21:26:24.479 -03:00 [INF] Contract 293051094048 finished: Profit=0.95, Win=True
2025-09-02 21:26:24.479 -03:00 [INF] [DEBUG] exit_time for 293051094048: sell_time_epoch=1756859672 -> 00:34:32.000
2025-09-02 21:26:24.479 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:26:24.479
2025-09-02 21:26:24.480 -03:00 [INF] [TIMING] Contract WIN at 21:26:24.480 - calling OnContractWin
2025-09-02 21:26:24.480 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:26:24.480 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:26:24.481 -03:00 [INF] Profit Table updated for contract 293051094048: Profit=0.95, ExitPrice=5964.482, ExitTime=00:34:32
2025-09-02 21:26:24.482 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051094048: 5964.512 at 00:34:30
2025-09-02 21:26:24.482 -03:00 [INF] [TIMING] ContractResult event concluído às 21:26:24.482 (duração: 2.9188ms)
2025-09-02 21:26:29.554 -03:00 [INF] [DEBUG] Fast Martingale DISABLED - clearing hot pool
2025-09-02 21:26:31.176 -03:00 [INF] [DEBUG] Contrato comprado: 293051103588, subscrevendo para atualizações
2025-09-02 21:26:31.177 -03:00 [INF] Compra executada com sucesso. ContractId: 293051103588, TransactionId: 583719206148
2025-09-02 21:26:31.177 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:26:31.177 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:26:32.520 -03:00 [INF] [DEBUG] entry_tick for 293051103588: epoch=1756859682 -> 00:34:42.000, price=5964.414
2025-09-02 21:26:32.521 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051103588: 5964.414 at 00:34:42
2025-09-02 21:26:32.522 -03:00 [INF] [DEBUG] entry_tick for 293051103588: epoch=1756859682 -> 00:34:42.000, price=5964.414
2025-09-02 21:26:32.523 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051103588: 5964.414 at 00:34:42
2025-09-02 21:26:34.483 -03:00 [INF] [DEBUG] entry_tick for 293051103588: epoch=1756859682 -> 00:34:42.000, price=5964.414
2025-09-02 21:26:34.483 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051103588: 5964.414 at 00:34:42
2025-09-02 21:26:34.483 -03:00 [INF] [DEBUG] entry_tick for 293051103588: epoch=1756859682 -> 00:34:42.000, price=5964.414
2025-09-02 21:26:34.483 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051103588: 5964.414 at 00:34:42
2025-09-02 21:26:36.488 -03:00 [INF] [DEBUG] entry_tick for 293051103588: epoch=1756859682 -> 00:34:42.000, price=5964.414
2025-09-02 21:26:36.488 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051103588: 5964.414 at 00:34:42
2025-09-02 21:26:36.489 -03:00 [INF] [TIMING] FALLBACK - Contrato 293051103588 detectado como finalizado
2025-09-02 21:26:36.489 -03:00 [INF] Contract 293051103588 finished: Profit=-1, Win=False
2025-09-02 21:26:36.489 -03:00 [INF] [DEBUG] exit_time for 293051103588: sell_time_epoch=1756859684 -> 00:34:44.000
2025-09-02 21:26:36.489 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:26:36.489
2025-09-02 21:26:36.489 -03:00 [INF] [TIMING] Contract LOSS at 21:26:36.489 - ZERO-DELAY EXECUTION
2025-09-02 21:26:36.490 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.0533ms
2025-09-02 21:26:36.490 -03:00 [INF] Profit Table updated for contract 293051103588: Profit=-1, ExitPrice=5964.525, ExitTime=00:34:44
2025-09-02 21:26:36.491 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051103588: 5964.414 at 00:34:42
2025-09-02 21:26:36.491 -03:00 [INF] [TIMING] ContractResult event concluído às 21:26:36.491 (duração: 2.153ms)
2025-09-02 21:26:39.348 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 21:26:39.348 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:26:39.348
2025-09-02 21:26:39.348 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:26:39.552 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 203.2632ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:39.552 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 203.6654ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:39.552 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 203.7124ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:39.569 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 220.074ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:39.569 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.6248ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:39.569 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 220.2934ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:26:39.569 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-02 21:26:39.569 -03:00 [INF] [DEBUG] Fast Martingale READY: 1 proposals pre-calculated and ready for instant execution
2025-09-02 21:26:41.093 -03:00 [INF] [DEBUG] Contrato comprado: 293051111368, subscrevendo para atualizações
2025-09-02 21:26:41.093 -03:00 [INF] Compra executada com sucesso. ContractId: 293051111368, TransactionId: 583719221788
2025-09-02 21:26:41.093 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:26:41.093
2025-09-02 21:26:41.093 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:26:41.093
2025-09-02 21:26:41.093 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:26:41.308 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 214.4041ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:41.308 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 214.5404ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:41.308 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 214.6501ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:41.311 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.4537ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:41.311 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.6519ms. Stake: 64.00, ProposalId: 44f18ffb-94d8-7d34-8509-8da12362f46b
2025-09-02 21:26:41.311 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 217.7994ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:26:41.312 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-02 21:26:41.312 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 218.1595ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:26:41.312 -03:00 [INF] [DEBUG] HOT POOL STATUS: 1 propostas prontas nos níveis: [6]
2025-09-02 21:26:41.312 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:26:41.312 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=2.00
2025-09-02 21:26:42.515 -03:00 [INF] [DEBUG] entry_tick for 293051111368: epoch=1756859692 -> 00:34:52.000, price=5965.06
2025-09-02 21:26:42.515 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051111368: 5965.06 at 00:34:52
2025-09-02 21:26:42.515 -03:00 [INF] [DEBUG] entry_tick for 293051111368: epoch=1756859692 -> 00:34:52.000, price=5965.06
2025-09-02 21:26:42.517 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051111368: 5965.06 at 00:34:52
2025-09-02 21:26:44.551 -03:00 [INF] [DEBUG] entry_tick for 293051111368: epoch=1756859692 -> 00:34:52.000, price=5965.06
2025-09-02 21:26:44.551 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051111368: 5965.06 at 00:34:52
2025-09-02 21:26:44.551 -03:00 [INF] [DEBUG] entry_tick for 293051111368: epoch=1756859692 -> 00:34:52.000, price=5965.06
2025-09-02 21:26:44.551 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051111368: 5965.06 at 00:34:52
2025-09-02 21:26:46.508 -03:00 [INF] [DEBUG] entry_tick for 293051111368: epoch=1756859692 -> 00:34:52.000, price=5965.06
2025-09-02 21:26:46.508 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051111368: 5965.06 at 00:34:52
2025-09-02 21:26:46.508 -03:00 [INF] [TIMING] FALLBACK - Contrato 293051111368 detectado como finalizado
2025-09-02 21:26:46.508 -03:00 [INF] Contract 293051111368 finished: Profit=0.95, Win=True
2025-09-02 21:26:46.508 -03:00 [INF] [DEBUG] exit_time for 293051111368: sell_time_epoch=1756859694 -> 00:34:54.000
2025-09-02 21:26:46.508 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:26:46.508
2025-09-02 21:26:46.508 -03:00 [INF] [TIMING] Contract WIN at 21:26:46.508 - calling OnContractWin
2025-09-02 21:26:46.509 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:26:46.509 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-02 21:26:46.509 -03:00 [INF] Profit Table updated for contract 293051111368: Profit=0.95, ExitPrice=5964.893, ExitTime=00:34:54
2025-09-02 21:26:46.511 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051111368: 5965.06 at 00:34:52
2025-09-02 21:26:46.511 -03:00 [INF] [TIMING] ContractResult event concluído às 21:26:46.511 (duração: 2.7482ms)
2025-09-02 21:26:52.948 -03:00 [INF] Application is shutting down...
2025-09-02 21:34:53.932 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-02 21:34:53.957 -03:00 [INF] Hosting environment: Production
2025-09-02 21:34:53.957 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-02 21:34:54.432 -03:00 [INF] Conectando à API Deriv...
2025-09-02 21:34:55.127 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-02 21:34:55.539 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:34:55.542 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:34:55.565 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-02 21:34:55.565 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-02 21:34:56.046 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:34:56.047 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:34:56.047 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:34:56.047 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:34:56.047 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:34:56.047 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:34:56.047 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:34:56.047 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:34:56.047 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:34:56.050 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-02 21:34:56.158 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-02 21:34:56.159 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-02 21:35:09.934 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:35:09.935 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:35:09.942 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-02 21:35:12.199 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:35:12.199 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:35:16.603 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-09-02 21:35:16.607 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:35:16.607
2025-09-02 21:35:16.607 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:35:16.820 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:16.828 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:16.828 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:16.829 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:16.830 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:16.831 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 223.5986ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:35:16.831 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:35:16.831 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-09-02 21:35:18.147 -03:00 [INF] [DEBUG] Contrato comprado: 293051543728, subscrevendo para atualizações
2025-09-02 21:35:18.147 -03:00 [INF] Compra executada com sucesso. ContractId: 293051543728, TransactionId: 583720058048
2025-09-02 21:35:18.152 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 21:35:18.152
2025-09-02 21:35:18.152 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:35:18.152
2025-09-02 21:35:18.152 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:35:18.348 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:18.348 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:18.363 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:18.492 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:18.492 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass268_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1446
2025-09-02 21:35:18.495 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 342.1077ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-09-02 21:35:18.495 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-09-02 21:35:18.495 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 342.3456ms - propostas GARANTIDAMENTE prontas
2025-09-02 21:35:18.495 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-09-02 21:35:18.495 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:35:18.495 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:35:18.555 -03:00 [INF] [DEBUG] entry_tick for 293051543728: epoch=1756860208 -> 00:43:28.000, price=5962.836
2025-09-02 21:35:18.556 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051543728: 5962.836 at 00:43:28
2025-09-02 21:35:18.557 -03:00 [INF] [DEBUG] entry_tick for 293051543728: epoch=1756860208 -> 00:43:28.000, price=5962.836
2025-09-02 21:35:18.558 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051543728: 5962.836 at 00:43:28
2025-09-02 21:35:20.486 -03:00 [INF] [DEBUG] entry_tick for 293051543728: epoch=1756860208 -> 00:43:28.000, price=5962.836
2025-09-02 21:35:20.491 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051543728: 5962.836 at 00:43:28
2025-09-02 21:35:20.492 -03:00 [INF] [DEBUG] entry_tick for 293051543728: epoch=1756860208 -> 00:43:28.000, price=5962.836
2025-09-02 21:35:20.492 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051543728: 5962.836 at 00:43:28
2025-09-02 21:35:22.497 -03:00 [INF] [DEBUG] entry_tick for 293051543728: epoch=1756860208 -> 00:43:28.000, price=5962.836
2025-09-02 21:35:22.497 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051543728: 5962.836 at 00:43:28
2025-09-02 21:35:22.497 -03:00 [INF] [TIMING] FALLBACK - Contrato 293051543728 detectado como finalizado
2025-09-02 21:35:22.497 -03:00 [INF] Contract 293051543728 finished: Profit=-1, Win=False
2025-09-02 21:35:22.497 -03:00 [INF] [DEBUG] exit_time for 293051543728: sell_time_epoch=1756860210 -> 00:43:30.000
2025-09-02 21:35:22.497 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:35:22.497
2025-09-02 21:35:22.497 -03:00 [INF] [TIMING] Contract LOSS at 21:35:22.497 - ZERO-DELAY EXECUTION
2025-09-02 21:35:22.498 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.3263ms. Level: 0 → 1, Stake: 2.00
2025-09-02 21:35:22.499 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.372ms. Level: 1
2025-09-02 21:35:22.499 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:35:22.499
2025-09-02 21:35:22.499 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.6208ms
2025-09-02 21:35:22.499 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.5528ms
2025-09-02 21:35:22.499 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:35:22.499
2025-09-02 21:35:22.499 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:35:22.501 -03:00 [INF] Profit Table updated for contract 293051543728: Profit=-1, ExitPrice=5962.801, ExitTime=00:43:30
2025-09-02 21:35:22.503 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051543728: 5962.836 at 00:43:28
2025-09-02 21:35:22.503 -03:00 [INF] [TIMING] ContractResult event concluído às 21:35:22.503 (duração: 5.9671ms)
2025-09-02 21:35:22.723 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 223.3089ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:22.723 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 224.2823ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:22.723 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 224.3853ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:22.724 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 224.2871ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:22.748 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 249.1071ms às 21:35:22.748
2025-09-02 21:35:22.748 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.3806ms às 21:35:22.748
2025-09-02 21:35:22.748 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 249.4877ms - TRUE INSTANT execution
2025-09-02 21:35:22.753 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 247.0972ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:22.754 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 254.5168ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:35:22.754 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-09-02 21:35:22.996 -03:00 [INF] Buy response processed: Contract 293051547408, Type: Unknown, Stake: 2
2025-09-02 21:35:22.996 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293051547408, Type: Unknown, PurchaseTime: 00:43:32
2025-09-02 21:35:24.490 -03:00 [INF] [DEBUG] entry_tick for 293051547408: epoch=1756860214 -> 00:43:34.000, price=5962.564
2025-09-02 21:35:24.492 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051547408: 5962.564 at 00:43:34
2025-09-02 21:35:26.484 -03:00 [INF] [DEBUG] entry_tick for 293051547408: epoch=1756860214 -> 00:43:34.000, price=5962.564
2025-09-02 21:35:26.486 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051547408: 5962.564 at 00:43:34
2025-09-02 21:35:28.578 -03:00 [INF] [DEBUG] entry_tick for 293051547408: epoch=1756860214 -> 00:43:34.000, price=5962.564
2025-09-02 21:35:28.579 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051547408: 5962.564 at 00:43:34
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] FALLBACK - Contrato 293051547408 detectado como finalizado
2025-09-02 21:35:28.579 -03:00 [INF] Contract 293051547408 finished: Profit=-2, Win=False
2025-09-02 21:35:28.579 -03:00 [INF] [DEBUG] exit_time for 293051547408: sell_time_epoch=1756860216 -> 00:43:36.000
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:35:28.579
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] Contract LOSS at 21:35:28.579 - ZERO-DELAY EXECUTION
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0447ms. Level: 1 → 2, Stake: 4.00
2025-09-02 21:35:28.579 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0598ms. Level: 2
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:35:28.579
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1283ms
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1396ms
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:35:28.579
2025-09-02 21:35:28.579 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:35:28.579 -03:00 [INF] Profit Table updated for contract 293051547408: Profit=-2, ExitPrice=5962.355, ExitTime=00:43:36
2025-09-02 21:35:28.579 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051547408: 5962.564 at 00:43:34
2025-09-02 21:35:28.579 -03:00 [INF] [TIMING] ContractResult event concluído às 21:35:28.579 (duração: 0.6115ms)
2025-09-02 21:35:28.805 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.7643ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:28.805 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.938ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:28.805 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 225.9559ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:28.808 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 229.3392ms às 21:35:28.808
2025-09-02 21:35:28.808 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0689ms às 21:35:28.808
2025-09-02 21:35:28.808 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 229.4081ms - TRUE INSTANT execution
2025-09-02 21:35:28.808 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 229.3169ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:28.821 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 242.2706ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:28.821 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 242.3679ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:35:28.821 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-09-02 21:35:29.060 -03:00 [INF] Buy response processed: Contract 293051551828, Type: Unknown, Stake: 4
2025-09-02 21:35:29.060 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293051551828, Type: Unknown, PurchaseTime: 00:43:38
2025-09-02 21:35:30.481 -03:00 [INF] [DEBUG] entry_tick for 293051551828: epoch=1756860220 -> 00:43:40.000, price=5962.378
2025-09-02 21:35:30.484 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051551828: 5962.378 at 00:43:40
2025-09-02 21:35:30.484 -03:00 [INF] [DEBUG] entry_tick for 293051551828: epoch=1756860220 -> 00:43:40.000, price=5962.378
2025-09-02 21:35:30.485 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051551828: 5962.378 at 00:43:40
2025-09-02 21:35:32.501 -03:00 [INF] [DEBUG] entry_tick for 293051551828: epoch=1756860220 -> 00:43:40.000, price=5962.378
2025-09-02 21:35:32.502 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051551828: 5962.378 at 00:43:40
2025-09-02 21:35:32.502 -03:00 [INF] [DEBUG] entry_tick for 293051551828: epoch=1756860220 -> 00:43:40.000, price=5962.378
2025-09-02 21:35:32.502 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051551828: 5962.378 at 00:43:40
2025-09-02 21:35:34.545 -03:00 [INF] [DEBUG] entry_tick for 293051551828: epoch=1756860220 -> 00:43:40.000, price=5962.378
2025-09-02 21:35:34.546 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051551828: 5962.378 at 00:43:40
2025-09-02 21:35:34.546 -03:00 [INF] [TIMING] FALLBACK - Contrato 293051551828 detectado como finalizado
2025-09-02 21:35:34.546 -03:00 [INF] Contract 293051551828 finished: Profit=-4, Win=False
2025-09-02 21:35:34.546 -03:00 [INF] [DEBUG] exit_time for 293051551828: sell_time_epoch=1756860222 -> 00:43:42.000
2025-09-02 21:35:34.546 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:35:34.546
2025-09-02 21:35:34.546 -03:00 [INF] [TIMING] Contract LOSS at 21:35:34.546 - ZERO-DELAY EXECUTION
2025-09-02 21:35:34.546 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0463ms. Level: 2 → 3, Stake: 8.00
2025-09-02 21:35:34.546 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.07ms. Level: 3
2025-09-02 21:35:34.546 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:35:34.546
2025-09-02 21:35:34.546 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1109ms
2025-09-02 21:35:34.546 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1351ms
2025-09-02 21:35:34.546 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 21:35:34.546
2025-09-02 21:35:34.546 -03:00 [INF] Profit Table updated for contract 293051551828: Profit=-4, ExitPrice=5962.178, ExitTime=00:43:42
2025-09-02 21:35:34.546 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-09-02 21:35:34.548 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051551828: 5962.378 at 00:43:40
2025-09-02 21:35:34.548 -03:00 [INF] [TIMING] ContractResult event concluído às 21:35:34.548 (duração: 2.533ms)
2025-09-02 21:35:34.752 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.5065ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:34.752 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.665ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:34.752 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 205.6688ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:34.763 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 207.619ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:34.764 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 217.679ms às 21:35:34.764
2025-09-02 21:35:34.764 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0552ms às 21:35:34.764
2025-09-02 21:35:34.764 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 217.7342ms - TRUE INSTANT execution
2025-09-02 21:35:34.764 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Level 6 populated in 217.4036ms. Stake: 64.00, ProposalId: bca2ba9d-7abd-3646-9a07-37f5ab0dfc95
2025-09-02 21:35:34.764 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 217.6273ms. Pool contains 1 proposals GUARANTEED ready. Levels: [6]
2025-09-02 21:35:34.764 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 3
2025-09-02 21:35:35.000 -03:00 [INF] Buy response processed: Contract 293051556788, Type: Unknown, Stake: 8
2025-09-02 21:35:35.000 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293051556788, Type: Unknown, PurchaseTime: 00:43:44
2025-09-02 21:35:36.507 -03:00 [INF] [DEBUG] entry_tick for 293051556788: epoch=1756860226 -> 00:43:46.000, price=5962.365
2025-09-02 21:35:36.509 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051556788: 5962.365 at 00:43:46
2025-09-02 21:35:36.509 -03:00 [INF] [DEBUG] entry_tick for 293051556788: epoch=1756860226 -> 00:43:46.000, price=5962.365
2025-09-02 21:35:36.510 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051556788: 5962.365 at 00:43:46
2025-09-02 21:35:38.511 -03:00 [INF] [DEBUG] entry_tick for 293051556788: epoch=1756860226 -> 00:43:46.000, price=5962.365
2025-09-02 21:35:38.511 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051556788: 5962.365 at 00:43:46
2025-09-02 21:35:38.523 -03:00 [INF] [DEBUG] entry_tick for 293051556788: epoch=1756860226 -> 00:43:46.000, price=5962.365
2025-09-02 21:35:38.523 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051556788: 5962.365 at 00:43:46
2025-09-02 21:35:40.501 -03:00 [INF] [DEBUG] entry_tick for 293051556788: epoch=1756860226 -> 00:43:46.000, price=5962.365
2025-09-02 21:35:40.503 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051556788: 5962.365 at 00:43:46
2025-09-02 21:35:40.503 -03:00 [INF] [TIMING] FALLBACK - Contrato 293051556788 detectado como finalizado
2025-09-02 21:35:40.503 -03:00 [INF] Contract 293051556788 finished: Profit=-8, Win=False
2025-09-02 21:35:40.503 -03:00 [INF] [DEBUG] exit_time for 293051556788: sell_time_epoch=1756860228 -> 00:43:48.000
2025-09-02 21:35:40.503 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:35:40.503
2025-09-02 21:35:40.503 -03:00 [INF] [TIMING] Contract LOSS at 21:35:40.503 - ZERO-DELAY EXECUTION
2025-09-02 21:35:40.504 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-02 21:35:40.504 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-02 21:35:40.504 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.601ms
2025-09-02 21:35:40.504 -03:00 [INF] Profit Table updated for contract 293051556788: Profit=-8, ExitPrice=5962.21, ExitTime=00:43:48
2025-09-02 21:35:40.504 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293051556788: 5962.365 at 00:43:46
2025-09-02 21:35:40.504 -03:00 [INF] [TIMING] ContractResult event concluído às 21:35:40.504 (duração: 0.9937ms)
