using System.Text.Json.Serialization;

namespace Excalibur.Models
{
    public class ProposalResponse
    {
        [JsonPropertyName("proposal")]
        public ProposalData? Proposal { get; set; }
        
        [JsonPropertyName("msg_type")]
        public string MessageType { get; set; } = string.Empty;
        
        [JsonPropertyName("error")]
        public ProposalError? Error { get; set; }
    }
    
    public class ProposalData
    {
        [JsonPropertyName("ask_price")]
        public decimal AskPrice { get; set; }
        
        [JsonPropertyName("payout")]
        public decimal Payout { get; set; }
        
        [JsonPropertyName("spot")]
        public decimal Spot { get; set; }
        
        [JsonPropertyName("barrier")]
        public string? Barrier { get; set; }
        
        [JsonPropertyName("high_barrier")]
        public string? HighBarrier { get; set; }
        
        [JsonPropertyName("low_barrier")]
        public string? LowBarrier { get; set; }
        
        [JsonPropertyName("display_value")]
        public string? DisplayValue { get; set; }
        
        [JsonPropertyName("id")]
        public string? Id { get; set; }
    }
    
    public class ProposalError
    {
        [Json<PERSON>ropertyName("code")]
        public string Code { get; set; } = string.Empty;
        
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;
    }
}