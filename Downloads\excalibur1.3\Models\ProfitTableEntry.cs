using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Excalibur.Models
{
    public class ProfitTableEntry : INotifyPropertyChanged
    {
        private string _refId = string.Empty;
        private string _contract = string.Empty;
        private string _duration = string.Empty;
        private DateTime? _entrySpot;
        private DateTime? _exitSpot;
        private decimal _stake;
        private decimal _totalProfitLoss;
        private bool _isActive = true;
        private decimal _currentPrice;
        private decimal _payout;
        private decimal? _entryPrice;
        private decimal? _exitPrice;

        public string RefId
        {
            get => _refId;
            set { _refId = value; OnPropertyChanged(); }
        }

        public string Contract
        {
            get => _contract;
            set { _contract = value; OnPropertyChanged(); }
        }

        public string Duration
        {
            get => _duration;
            set { _duration = value; OnPropertyChanged(); }
        }

        public DateTime? EntrySpot
        {
            get => _entrySpot;
            set { _entrySpot = value; OnPropertyChanged(); OnPropertyChanged(nameof(EntrySpotDisplay)); }
        }

        public DateTime? ExitSpot
        {
            get => _exitSpot;
            set { _exitSpot = value; OnPropertyChanged(); OnPropertyChanged(nameof(ExitSpotDisplay)); }
        }

        public decimal Stake
        {
            get => _stake;
            set { _stake = value; OnPropertyChanged(); OnPropertyChanged(nameof(StakeDisplay)); }
        }

        public decimal TotalProfitLoss
        {
            get => _totalProfitLoss;
            set 
            { 
                _totalProfitLoss = value; 
                OnPropertyChanged(); 
                OnPropertyChanged(nameof(TotalProfitLossDisplay));
                OnPropertyChanged(nameof(ProfitLossColor));
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(); }
        }

        public decimal CurrentPrice
        {
            get => _currentPrice;
            set { _currentPrice = value; OnPropertyChanged(); UpdateProfitLoss(); }
        }

        public decimal Payout
        {
            get => _payout;
            set { _payout = value; OnPropertyChanged(); }
        }

        public decimal? EntryPrice
        {
            get => _entryPrice;
            set { _entryPrice = value; OnPropertyChanged(); OnPropertyChanged(nameof(EntrySpotDisplay)); }
        }

        public decimal? ExitPrice
        {
            get => _exitPrice;
            set { _exitPrice = value; OnPropertyChanged(); OnPropertyChanged(nameof(ExitSpotDisplay)); }
        }

        // Display properties: show the API spot price with exactly 3 decimal places.
        // When the API time is available, append it discreetly in parentheses (HH:mm:ss).
        // If the API has not provided the price yet, display '---'.
        public string EntrySpotDisplay
        {
            get
            {
                if (!EntryPrice.HasValue) return "---";
                var price = EntryPrice.Value.ToString("F3");
                if (EntrySpot.HasValue)
                {
                    // Use UTC time from the API timestamp and show only time portion
                    return $"{price} ({EntrySpot.Value.ToUniversalTime():HH:mm:ss})";
                }
                return price;
            }
        }

        public string ExitSpotDisplay
        {
            get
            {
                if (!ExitPrice.HasValue) return "---";
                var price = ExitPrice.Value.ToString("F3");
                if (ExitSpot.HasValue)
                {
                    return $"{price} ({ExitSpot.Value.ToUniversalTime():HH:mm:ss})";
                }
                return price;
            }
        }
        public string StakeDisplay => Stake.ToString("F2");
        public string TotalProfitLossDisplay => TotalProfitLoss.ToString("F2");
        public string ProfitLossColor => TotalProfitLoss >= 0 ? "#FF2ECC71" : "#FFFF4444";

        private void UpdateProfitLoss()
        {
            if (IsActive && CurrentPrice > 0 && Payout > 0)
            {
                // For binary options, the profit/loss calculation is different
                // We'll use a simplified approach here - the real calculation will come from the API
                // This is just for visual feedback during active contracts
                var priceMovement = CurrentPrice - EntryPrice;
                var estimatedProfit = priceMovement > 0 ? (Payout - Stake) : -Stake;

                // Only update if we don't have the final result yet
                if (IsActive)
                {
                    TotalProfitLoss = estimatedProfit;
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
