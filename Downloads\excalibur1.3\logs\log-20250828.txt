2025-08-28 22:12:36.257 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-28 22:12:36.279 -03:00 [INF] Hosting environment: Production
2025-08-28 22:12:36.280 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-28 22:12:36.576 -03:00 [INF] Conectando à API Deriv...
2025-08-28 22:12:42.719 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-28 22:12:43.105 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-28 22:12:43.108 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-28 22:12:43.122 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-28 22:12:43.123 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-28 22:12:43.638 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-28 22:12:43.638 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-28 22:12:43.639 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-28 22:12:43.639 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-28 22:12:43.639 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-28 22:12:43.639 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-28 22:12:43.639 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-28 22:12:43.639 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-28 22:12:43.639 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-28 22:12:43.658 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-28 22:12:43.761 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-28 22:12:43.761 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-28 22:12:43.762 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-28 22:12:43.762 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-28 22:12:43.762 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-28 22:12:43.762 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-28 22:12:43.762 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-28 22:12:43.762 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-28 22:12:43.762 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-28 22:12:43.762 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-28 22:12:50.995 -03:00 [INF] Application is shutting down...
2025-08-28 22:24:55.682 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-28 22:24:55.707 -03:00 [INF] Hosting environment: Production
2025-08-28 22:24:55.708 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-28 22:24:56.066 -03:00 [INF] Conectando à API Deriv...
2025-08-28 22:25:01.410 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-28 22:25:01.925 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-28 22:25:01.934 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-28 22:25:02.096 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-28 22:25:02.096 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-28 22:25:02.424 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-28 22:25:02.425 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-28 22:25:02.426 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-28 22:25:02.426 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-28 22:25:02.426 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-28 22:25:02.426 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-28 22:25:02.426 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-28 22:25:02.426 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-28 22:25:02.426 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-28 22:25:02.430 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-28 22:25:02.440 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-28 22:25:02.440 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-28 22:25:02.440 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-28 22:25:02.441 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-28 22:25:02.441 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-28 22:25:02.441 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-28 22:25:02.441 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-28 22:25:02.441 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-28 22:25:02.441 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-28 22:25:02.441 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-28 22:25:47.278 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:25:47.279 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-08-28 22:25:47.286 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-28 22:25:49.773 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:25:49.773 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-08-28 22:25:53.514 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:25:53.514 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-08-28 22:25:53.797 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:25:53.797 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-08-28 22:25:53.980 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:25:53.980 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.4
2025-08-28 22:26:09.701 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-08-28 22:26:09.703 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:26:09.703
2025-08-28 22:26:09.703 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:26:09.932 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:10.053 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:10.053 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:10.053 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:10.053 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:10.054 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 350.6339ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:26:10.054 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:26:10.054 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-08-28 22:26:16.831 -03:00 [INF] [DEBUG] Contrato comprado: 292563112648, subscrevendo para atualizações
2025-08-28 22:26:16.831 -03:00 [INF] Compra executada com sucesso. ContractId: 292563112648, TransactionId: 582770712668
2025-08-28 22:26:16.833 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:26:16.833
2025-08-28 22:26:16.833 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:26:16.833
2025-08-28 22:26:16.833 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:26:17.017 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:17.017 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:17.019 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:17.033 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:17.037 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:17.038 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 205.6409ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:26:17.039 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:26:17.039 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 205.9074ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:26:17.039 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:26:17.039 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:26:17.039 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.4
2025-08-28 22:26:20.794 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563112648 detectado como finalizado às 22:26:20.794
2025-08-28 22:26:20.794 -03:00 [INF] Contract 292563112648 finished: Profit=-0.4, Win=False
2025-08-28 22:26:20.794 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:26:20.794
2025-08-28 22:26:20.795 -03:00 [INF] [TIMING] Contract LOSS at 22:26:20.795 - ZERO-DELAY EXECUTION
2025-08-28 22:26:20.796 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.1173ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:26:20.796 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.1785ms. Level: 1
2025-08-28 22:26:20.796 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:26:20.796
2025-08-28 22:26:20.796 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.8792ms
2025-08-28 22:26:20.796 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.7492ms
2025-08-28 22:26:20.797 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:26:20.797
2025-08-28 22:26:20.797 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:26:20.797 -03:00 [INF] Profit Table updated for contract 292563112648: Profit=-0.4, ExitPrice=6083.968
2025-08-28 22:26:20.797 -03:00 [INF] [TIMING] ContractResult event concluído às 22:26:20.797 (duração: 2.9243ms)
2025-08-28 22:26:20.988 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:20.997 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:20.997 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:21.006 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:21.007 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:26:21.007 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 210.1521ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:26:21.007 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:26:21.007 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 210.5941ms às 22:26:21.007
2025-08-28 22:26:21.007 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0744ms às 22:26:21.007
2025-08-28 22:26:21.007 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 210.6685ms - TRUE INSTANT execution
2025-08-28 22:26:21.260 -03:00 [INF] Buy response processed: Contract 292563116248, Type: Unknown, Stake: 0.84
2025-08-28 22:26:21.262 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563116248, Type: Unknown
2025-08-28 22:26:24.838 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563116248 detectado como finalizado às 22:26:24.838
2025-08-28 22:26:24.838 -03:00 [INF] Contract 292563116248 finished: Profit=0.8, Win=True
2025-08-28 22:26:24.838 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:26:24.838
2025-08-28 22:26:24.838 -03:00 [INF] [TIMING] Contract WIN at 22:26:24.838 - calling OnContractWin
2025-08-28 22:26:24.838 -03:00 [INF] Profit Table updated for contract 292563116248: Profit=0.8, ExitPrice=6083.997
2025-08-28 22:26:24.838 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:26:24.838 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:26:24.838 -03:00 [INF] [TIMING] ContractResult event concluído às 22:26:24.838 (duração: 0.3526ms)
2025-08-28 22:27:00.240 -03:00 [INF] [DEBUG] Contrato comprado: 292563141968, subscrevendo para atualizações
2025-08-28 22:27:00.240 -03:00 [INF] Compra executada com sucesso. ContractId: 292563141968, TransactionId: 582770771468
2025-08-28 22:27:00.240 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:27:00.240
2025-08-28 22:27:00.240 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:27:00.240
2025-08-28 22:27:00.240 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:27:00.453 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:00.453 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:00.453 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:00.454 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:00.454 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:00.454 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 214.5723ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:27:00.454 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:27:00.454 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 214.6619ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:27:00.454 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:27:00.454 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:27:00.455 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:27:02.835 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563141968 detectado como finalizado às 22:27:02.835
2025-08-28 22:27:02.835 -03:00 [INF] Contract 292563141968 finished: Profit=0.36, Win=True
2025-08-28 22:27:02.835 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:27:02.835
2025-08-28 22:27:02.835 -03:00 [INF] [TIMING] Contract WIN at 22:27:02.835 - calling OnContractWin
2025-08-28 22:27:02.836 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:27:02.836 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:27:02.836 -03:00 [INF] Profit Table updated for contract 292563141968: Profit=0.36, ExitPrice=6084.756
2025-08-28 22:27:02.836 -03:00 [INF] [TIMING] ContractResult event concluído às 22:27:02.836 (duração: 0.8391ms)
2025-08-28 22:27:24.325 -03:00 [INF] [DEBUG] Contrato comprado: 292563160068, subscrevendo para atualizações
2025-08-28 22:27:24.325 -03:00 [INF] Compra executada com sucesso. ContractId: 292563160068, TransactionId: 582770806708
2025-08-28 22:27:24.325 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:27:24.325
2025-08-28 22:27:24.325 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:27:24.325
2025-08-28 22:27:24.325 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:27:24.509 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:24.510 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:24.519 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:24.519 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:24.545 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:24.546 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 220.9372ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:27:24.546 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:27:24.546 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 221.0223ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:27:24.546 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:27:24.546 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:27:24.546 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:27:28.869 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563160068 detectado como finalizado às 22:27:28.869
2025-08-28 22:27:28.869 -03:00 [INF] Contract 292563160068 finished: Profit=-0.4, Win=False
2025-08-28 22:27:28.869 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:27:28.869
2025-08-28 22:27:28.869 -03:00 [INF] [TIMING] Contract LOSS at 22:27:28.869 - ZERO-DELAY EXECUTION
2025-08-28 22:27:28.869 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0452ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:27:28.869 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0743ms. Level: 1
2025-08-28 22:27:28.869 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:27:28.869
2025-08-28 22:27:28.869 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.2852ms
2025-08-28 22:27:28.869 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.307ms
2025-08-28 22:27:28.869 -03:00 [INF] Profit Table updated for contract 292563160068: Profit=-0.4, ExitPrice=6084.895
2025-08-28 22:27:28.870 -03:00 [INF] [TIMING] ContractResult event concluído às 22:27:28.870 (duração: 0.5096ms)
2025-08-28 22:27:28.872 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:27:28.872
2025-08-28 22:27:28.872 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:27:29.108 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:29.108 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:29.108 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:29.109 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:29.109 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 239.5588ms às 22:27:29.109
2025-08-28 22:27:29.109 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0628ms às 22:27:29.109
2025-08-28 22:27:29.109 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 239.6216ms - TRUE INSTANT execution
2025-08-28 22:27:29.109 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:27:29.109 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 237.1155ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:27:29.109 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:27:29.389 -03:00 [INF] Buy response processed: Contract 292563163828, Type: Unknown, Stake: 0.84
2025-08-28 22:27:29.390 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563163828, Type: Unknown
2025-08-28 22:27:32.873 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563163828 detectado como finalizado às 22:27:32.873
2025-08-28 22:27:32.873 -03:00 [INF] Contract 292563163828 finished: Profit=0.8, Win=True
2025-08-28 22:27:32.873 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:27:32.873
2025-08-28 22:27:32.873 -03:00 [INF] [TIMING] Contract WIN at 22:27:32.873 - calling OnContractWin
2025-08-28 22:27:32.874 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:27:32.874 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:27:32.874 -03:00 [INF] Profit Table updated for contract 292563163828: Profit=0.8, ExitPrice=6084.692
2025-08-28 22:27:32.874 -03:00 [INF] [TIMING] ContractResult event concluído às 22:27:32.874 (duração: 0.2342ms)
2025-08-28 22:28:43.786 -03:00 [INF] [DEBUG] Contrato comprado: 292563215268, subscrevendo para atualizações
2025-08-28 22:28:43.786 -03:00 [INF] Compra executada com sucesso. ContractId: 292563215268, TransactionId: 582770918168
2025-08-28 22:28:43.786 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:28:43.786
2025-08-28 22:28:43.786 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:28:43.786
2025-08-28 22:28:43.786 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:28:43.976 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:43.992 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:43.993 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:43.996 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:43.997 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:43.997 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 211.1295ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:28:43.997 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:28:43.997 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 211.3079ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:28:43.997 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:28:43.997 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:28:43.997 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:28:48.869 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563215268 detectado como finalizado às 22:28:48.869
2025-08-28 22:28:48.869 -03:00 [INF] Contract 292563215268 finished: Profit=0.36, Win=True
2025-08-28 22:28:48.869 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:28:48.869
2025-08-28 22:28:48.869 -03:00 [INF] [TIMING] Contract WIN at 22:28:48.869 - calling OnContractWin
2025-08-28 22:28:48.869 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:28:48.869 -03:00 [INF] Profit Table updated for contract 292563215268: Profit=0.36, ExitPrice=6084.201
2025-08-28 22:28:48.869 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:28:48.869 -03:00 [INF] [TIMING] ContractResult event concluído às 22:28:48.869 (duração: 0.2581ms)
2025-08-28 22:28:58.771 -03:00 [INF] [DEBUG] Contrato comprado: 292563224468, subscrevendo para atualizações
2025-08-28 22:28:58.771 -03:00 [INF] Compra executada com sucesso. ContractId: 292563224468, TransactionId: 582770936748
2025-08-28 22:28:58.771 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:28:58.771
2025-08-28 22:28:58.771 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:28:58.771
2025-08-28 22:28:58.771 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:28:58.959 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:58.967 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:58.968 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:58.970 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:58.974 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:28:58.974 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 202.8191ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:28:58.974 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:28:58.974 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 203.0396ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:28:58.974 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:28:58.974 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:28:58.974 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:29:04.844 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563224468 detectado como finalizado às 22:29:04.844
2025-08-28 22:29:04.844 -03:00 [INF] Contract 292563224468 finished: Profit=0.36, Win=True
2025-08-28 22:29:04.844 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:29:04.844
2025-08-28 22:29:04.844 -03:00 [INF] [TIMING] Contract WIN at 22:29:04.844 - calling OnContractWin
2025-08-28 22:29:04.844 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:29:04.844 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:29:04.845 -03:00 [INF] Profit Table updated for contract 292563224468: Profit=0.36, ExitPrice=6083.743
2025-08-28 22:29:04.845 -03:00 [INF] [TIMING] ContractResult event concluído às 22:29:04.845 (duração: 0.3598ms)
2025-08-28 22:29:18.058 -03:00 [INF] [DEBUG] Contrato comprado: 292563238208, subscrevendo para atualizações
2025-08-28 22:29:18.058 -03:00 [INF] Compra executada com sucesso. ContractId: 292563238208, TransactionId: 582770963728
2025-08-28 22:29:18.058 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:29:18.058
2025-08-28 22:29:18.058 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:18.058
2025-08-28 22:29:18.058 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:29:18.260 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:18.260 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:18.260 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:18.266 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:18.280 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:18.281 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 222.9918ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:29:18.281 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:29:18.281 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 223.0685ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:29:18.281 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:29:18.281 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:29:18.281 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:29:20.897 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563238208 detectado como finalizado às 22:29:20.897
2025-08-28 22:29:20.897 -03:00 [INF] Contract 292563238208 finished: Profit=0.36, Win=True
2025-08-28 22:29:20.897 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:29:20.897
2025-08-28 22:29:20.897 -03:00 [INF] [TIMING] Contract WIN at 22:29:20.897 - calling OnContractWin
2025-08-28 22:29:20.897 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:29:20.897 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:29:20.898 -03:00 [INF] Profit Table updated for contract 292563238208: Profit=0.36, ExitPrice=6084.084
2025-08-28 22:29:20.898 -03:00 [INF] [TIMING] ContractResult event concluído às 22:29:20.898 (duração: 0.2755ms)
2025-08-28 22:29:26.332 -03:00 [INF] [DEBUG] Contrato comprado: 292563243248, subscrevendo para atualizações
2025-08-28 22:29:26.332 -03:00 [INF] Compra executada com sucesso. ContractId: 292563243248, TransactionId: 582770973768
2025-08-28 22:29:26.332 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:29:26.332
2025-08-28 22:29:26.332 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:26.332
2025-08-28 22:29:26.332 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:29:26.520 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:26.520 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:26.520 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:26.520 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:26.524 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:26.524 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 191.9552ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:29:26.524 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:29:26.524 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 192.1236ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:29:26.524 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:29:26.524 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:29:26.524 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:29:30.921 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563243248 detectado como finalizado às 22:29:30.921
2025-08-28 22:29:30.921 -03:00 [INF] Contract 292563243248 finished: Profit=-0.4, Win=False
2025-08-28 22:29:30.921 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:29:30.921
2025-08-28 22:29:30.921 -03:00 [INF] [TIMING] Contract LOSS at 22:29:30.921 - ZERO-DELAY EXECUTION
2025-08-28 22:29:30.921 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0405ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:29:30.921 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0529ms. Level: 1
2025-08-28 22:29:30.921 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:29:30.921
2025-08-28 22:29:30.921 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.083ms
2025-08-28 22:29:30.921 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.0978ms
2025-08-28 22:29:30.921 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:30.921
2025-08-28 22:29:30.921 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:29:30.922 -03:00 [INF] Profit Table updated for contract 292563243248: Profit=-0.4, ExitPrice=6083.986
2025-08-28 22:29:30.922 -03:00 [INF] [TIMING] ContractResult event concluído às 22:29:30.922 (duração: 0.4863ms)
2025-08-28 22:29:31.117 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 195.3082ms às 22:29:31.116
2025-08-28 22:29:31.117 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1178ms às 22:29:31.117
2025-08-28 22:29:31.117 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 195.426ms - TRUE INSTANT execution
2025-08-28 22:29:31.117 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:31.117 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:31.128 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:31.131 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:31.131 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:31.132 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 210.4477ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:29:31.132 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:29:31.355 -03:00 [INF] Buy response processed: Contract 292563246748, Type: Unknown, Stake: 0.84
2025-08-28 22:29:31.356 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563246748, Type: Unknown
2025-08-28 22:29:36.815 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563246748 detectado como finalizado às 22:29:36.815
2025-08-28 22:29:36.815 -03:00 [INF] Contract 292563246748 finished: Profit=0.8, Win=True
2025-08-28 22:29:36.815 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:29:36.815
2025-08-28 22:29:36.815 -03:00 [INF] [TIMING] Contract WIN at 22:29:36.815 - calling OnContractWin
2025-08-28 22:29:36.815 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:29:36.816 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:29:36.817 -03:00 [INF] Profit Table updated for contract 292563246748: Profit=0.8, ExitPrice=6084.221
2025-08-28 22:29:36.817 -03:00 [INF] [TIMING] ContractResult event concluído às 22:29:36.817 (duração: 1.5535ms)
2025-08-28 22:29:53.775 -03:00 [INF] [DEBUG] Contrato comprado: 292563260888, subscrevendo para atualizações
2025-08-28 22:29:53.775 -03:00 [INF] Compra executada com sucesso. ContractId: 292563260888, TransactionId: 582771008788
2025-08-28 22:29:53.775 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:29:53.775
2025-08-28 22:29:53.775 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:53.775
2025-08-28 22:29:53.775 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:29:53.952 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:53.962 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:53.963 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:53.963 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:53.970 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:53.971 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 195.724ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:29:53.971 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:29:53.971 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 196.0027ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:29:53.971 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:29:53.971 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:29:53.972 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:29:59.193 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563260888 detectado como finalizado às 22:29:59.193
2025-08-28 22:29:59.193 -03:00 [INF] Contract 292563260888 finished: Profit=-0.4, Win=False
2025-08-28 22:29:59.193 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:29:59.193
2025-08-28 22:29:59.193 -03:00 [INF] [TIMING] Contract LOSS at 22:29:59.193 - ZERO-DELAY EXECUTION
2025-08-28 22:29:59.193 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0192ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:29:59.193 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0279ms. Level: 1
2025-08-28 22:29:59.193 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:29:59.193
2025-08-28 22:29:59.193 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.0474ms
2025-08-28 22:29:59.193 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.0567ms
2025-08-28 22:29:59.196 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:29:59.196
2025-08-28 22:29:59.196 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:29:59.196 -03:00 [INF] Profit Table updated for contract 292563260888: Profit=-0.4, ExitPrice=6083.998
2025-08-28 22:29:59.196 -03:00 [INF] [TIMING] ContractResult event concluído às 22:29:59.196 (duração: 2.8579ms)
2025-08-28 22:29:59.992 -03:00 [ERR] Erro ao obter hot proposal
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetHotProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 909
2025-08-28 22:29:59.993 -03:00 [INF] [TIMING] INSTANT POOL: Sem proposta disponível, criando nova
2025-08-28 22:29:59.996 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:59.996 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:59.996 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:59.996 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:59.997 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:29:59.998 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 802.0313ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:29:59.998 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:30:00.793 -03:00 [ERR] [TIMING] INSTANT POOL BUY: Erro após 1599.9775ms
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 348
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.Services.DerivApiService.<>c__DisplayClass63_0.<<BuyInstantMarketAsync>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 822
2025-08-28 22:30:33.617 -03:00 [INF] [DEBUG] Contrato comprado: 292563287208, subscrevendo para atualizações
2025-08-28 22:30:33.617 -03:00 [INF] Compra executada com sucesso. ContractId: 292563287208, TransactionId: 582771061608
2025-08-28 22:30:33.617 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:30:33.617
2025-08-28 22:30:33.617 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:30:33.617
2025-08-28 22:30:33.617 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:30:33.804 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:33.805 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:33.805 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:33.827 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:33.828 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:33.828 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 211.5301ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:30:33.828 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:30:33.828 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 211.6934ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:30:33.828 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:30:33.828 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:30:33.828 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.84
2025-08-28 22:30:38.939 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563287208 detectado como finalizado às 22:30:38.939
2025-08-28 22:30:38.939 -03:00 [INF] Contract 292563287208 finished: Profit=0.36, Win=True
2025-08-28 22:30:38.939 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:30:38.939
2025-08-28 22:30:38.939 -03:00 [INF] [TIMING] Contract WIN at 22:30:38.939 - calling OnContractWin
2025-08-28 22:30:38.939 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:30:38.939 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:30:38.939 -03:00 [INF] Profit Table updated for contract 292563287208: Profit=0.36, ExitPrice=6084.149
2025-08-28 22:30:38.941 -03:00 [INF] [TIMING] ContractResult event concluído às 22:30:38.939 (duração: 0.6097ms)
2025-08-28 22:30:42.499 -03:00 [INF] [DEBUG] Contrato comprado: 292563292888, subscrevendo para atualizações
2025-08-28 22:30:42.499 -03:00 [INF] Compra executada com sucesso. ContractId: 292563292888, TransactionId: 582771073028
2025-08-28 22:30:42.499 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:30:42.499
2025-08-28 22:30:42.500 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:30:42.500
2025-08-28 22:30:42.500 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:30:42.707 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:42.708 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:42.708 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:42.718 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:42.722 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:42.722 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 222.2695ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:30:42.722 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:30:42.722 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 222.4562ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:30:42.722 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:30:42.722 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:30:42.722 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:30:48.827 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563292888 detectado como finalizado às 22:30:48.827
2025-08-28 22:30:48.827 -03:00 [INF] Contract 292563292888 finished: Profit=-0.4, Win=False
2025-08-28 22:30:48.827 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:30:48.827
2025-08-28 22:30:48.827 -03:00 [INF] [TIMING] Contract LOSS at 22:30:48.827 - ZERO-DELAY EXECUTION
2025-08-28 22:30:48.827 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0791ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:30:48.827 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.1084ms. Level: 1
2025-08-28 22:30:48.827 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:30:48.827
2025-08-28 22:30:48.827 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1633ms
2025-08-28 22:30:48.827 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1926ms
2025-08-28 22:30:48.828 -03:00 [INF] Profit Table updated for contract 292563292888: Profit=-0.4, ExitPrice=6084.084
2025-08-28 22:30:48.828 -03:00 [INF] [TIMING] ContractResult event concluído às 22:30:48.828 (duração: 1.0929ms)
2025-08-28 22:30:48.829 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:30:48.829
2025-08-28 22:30:48.829 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:30:49.025 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:49.038 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:49.042 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:49.042 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:49.042 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 214.9812ms às 22:30:49.042
2025-08-28 22:30:49.042 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0612ms às 22:30:49.042
2025-08-28 22:30:49.042 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 215.0424ms - TRUE INSTANT execution
2025-08-28 22:30:49.048 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:49.048 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 219.0882ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:30:49.048 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:30:49.278 -03:00 [INF] Buy response processed: Contract 292563296888, Type: Unknown, Stake: 0.84
2025-08-28 22:30:49.279 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563296888, Type: Unknown
2025-08-28 22:30:54.875 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563296888 detectado como finalizado às 22:30:54.875
2025-08-28 22:30:54.875 -03:00 [INF] Contract 292563296888 finished: Profit=-0.84, Win=False
2025-08-28 22:30:54.875 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:30:54.875
2025-08-28 22:30:54.875 -03:00 [INF] [TIMING] Contract LOSS at 22:30:54.875 - ZERO-DELAY EXECUTION
2025-08-28 22:30:54.875 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0558ms. Level: 1 → 2, Stake: 1.76
2025-08-28 22:30:54.875 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0773ms. Level: 2
2025-08-28 22:30:54.875 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:30:54.875
2025-08-28 22:30:54.875 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1159ms
2025-08-28 22:30:54.875 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.144ms
2025-08-28 22:30:54.875 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:30:54.875
2025-08-28 22:30:54.875 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:30:54.875 -03:00 [INF] Profit Table updated for contract 292563296888: Profit=-0.84, ExitPrice=6084.509
2025-08-28 22:30:54.876 -03:00 [INF] [TIMING] ContractResult event concluído às 22:30:54.876 (duração: 0.7285ms)
2025-08-28 22:30:55.066 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:55.069 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 193.8321ms às 22:30:55.069
2025-08-28 22:30:55.069 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1009ms às 22:30:55.069
2025-08-28 22:30:55.069 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 193.933ms - TRUE INSTANT execution
2025-08-28 22:30:55.079 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:55.079 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:55.079 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:55.083 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:55.083 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 207.6595ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:30:55.083 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-08-28 22:30:55.318 -03:00 [INF] Buy response processed: Contract 292563300788, Type: Unknown, Stake: 1.76
2025-08-28 22:30:55.318 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563300788, Type: Unknown
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563300788 detectado como finalizado às 22:30:58.854
2025-08-28 22:30:58.854 -03:00 [INF] Contract 292563300788 finished: Profit=-1.76, Win=False
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:30:58.854
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] Contract LOSS at 22:30:58.854 - ZERO-DELAY EXECUTION
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0496ms. Level: 2 → 3, Stake: 3.70
2025-08-28 22:30:58.854 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0725ms. Level: 3
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:30:58.854
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1139ms
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1425ms
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:30:58.854
2025-08-28 22:30:58.854 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:30:58.854 -03:00 [INF] Profit Table updated for contract 292563300788: Profit=-1.76, ExitPrice=6084.134
2025-08-28 22:30:58.854 -03:00 [INF] [TIMING] ContractResult event concluído às 22:30:58.854 (duração: 0.42ms)
2025-08-28 22:30:59.041 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:59.044 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:59.044 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 190.0362ms às 22:30:59.044
2025-08-28 22:30:59.044 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0314ms às 22:30:59.044
2025-08-28 22:30:59.044 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 190.0676ms - TRUE INSTANT execution
2025-08-28 22:30:59.044 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:59.047 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:59.055 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:30:59.055 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 200.9851ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:30:59.055 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 3
2025-08-28 22:30:59.266 -03:00 [INF] Buy response processed: Contract 292563303408, Type: Unknown, Stake: 3.7
2025-08-28 22:30:59.266 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563303408, Type: Unknown
2025-08-28 22:31:04.964 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563303408 detectado como finalizado às 22:31:04.964
2025-08-28 22:31:04.965 -03:00 [INF] Contract 292563303408 finished: Profit=-3.7, Win=False
2025-08-28 22:31:04.965 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:31:04.965
2025-08-28 22:31:04.965 -03:00 [INF] [TIMING] Contract LOSS at 22:31:04.965 - ZERO-DELAY EXECUTION
2025-08-28 22:31:04.965 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0589ms. Level: 3 → 4, Stake: 7.78
2025-08-28 22:31:04.965 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0792ms. Level: 4
2025-08-28 22:31:04.965 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:31:04.965
2025-08-28 22:31:04.965 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1167ms
2025-08-28 22:31:04.965 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.146ms
2025-08-28 22:31:04.965 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:31:04.965
2025-08-28 22:31:04.965 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:31:04.967 -03:00 [INF] Profit Table updated for contract 292563303408: Profit=-3.7, ExitPrice=6084.448
2025-08-28 22:31:04.967 -03:00 [INF] [TIMING] ContractResult event concluído às 22:31:04.967 (duração: 2.4783ms)
2025-08-28 22:31:05.192 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 226.8318ms às 22:31:05.191
2025-08-28 22:31:05.192 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0702ms às 22:31:05.192
2025-08-28 22:31:05.192 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 226.902ms - TRUE INSTANT execution
2025-08-28 22:31:05.192 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:05.192 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:05.192 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:05.224 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:05.224 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:05.224 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 259.3049ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:31:05.224 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 4
2025-08-28 22:31:05.429 -03:00 [INF] Buy response processed: Contract 292563308488, Type: Unknown, Stake: 7.78
2025-08-28 22:31:05.429 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563308488, Type: Unknown
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563308488 detectado como finalizado às 22:31:10.890
2025-08-28 22:31:10.890 -03:00 [INF] Contract 292563308488 finished: Profit=-7.78, Win=False
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:31:10.890
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] Contract LOSS at 22:31:10.890 - ZERO-DELAY EXECUTION
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0489ms. Level: 4 → 5, Stake: 16.34
2025-08-28 22:31:10.890 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0677ms. Level: 5
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:31:10.890
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.0998ms
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1176ms
2025-08-28 22:31:10.890 -03:00 [INF] Profit Table updated for contract 292563308488: Profit=-7.78, ExitPrice=6084.484
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:31:10.890
2025-08-28 22:31:10.890 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:31:10.890 -03:00 [INF] [TIMING] ContractResult event concluído às 22:31:10.890 (duração: 0.5067ms)
2025-08-28 22:31:11.093 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:11.093 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:11.094 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:11.099 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 208.6383ms às 22:31:11.099
2025-08-28 22:31:11.099 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0904ms às 22:31:11.099
2025-08-28 22:31:11.099 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 208.7287ms - TRUE INSTANT execution
2025-08-28 22:31:11.099 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:11.099 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:11.099 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 209.0074ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:31:11.099 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 5
2025-08-28 22:31:11.348 -03:00 [INF] Buy response processed: Contract 292563312688, Type: Unknown, Stake: 16.34
2025-08-28 22:31:11.349 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563312688, Type: Unknown
2025-08-28 22:31:16.774 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563312688 detectado como finalizado às 22:31:16.774
2025-08-28 22:31:16.774 -03:00 [INF] Contract 292563312688 finished: Profit=-16.34, Win=False
2025-08-28 22:31:16.774 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:31:16.774
2025-08-28 22:31:16.774 -03:00 [INF] [TIMING] Contract LOSS at 22:31:16.774 - ZERO-DELAY EXECUTION
2025-08-28 22:31:16.774 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.1033ms. Level: 5 → 6, Stake: 34.31
2025-08-28 22:31:16.774 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.1464ms. Level: 6
2025-08-28 22:31:16.774 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:31:16.774
2025-08-28 22:31:16.774 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.234ms
2025-08-28 22:31:16.775 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.2741ms
2025-08-28 22:31:16.775 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:31:16.775
2025-08-28 22:31:16.775 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:31:16.793 -03:00 [INF] Profit Table updated for contract 292563312688: Profit=-16.34, ExitPrice=6084.403
2025-08-28 22:31:16.793 -03:00 [INF] [TIMING] ContractResult event concluído às 22:31:16.793 (duração: 19.2232ms)
2025-08-28 22:31:16.978 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:16.978 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:16.979 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 204.1475ms às 22:31:16.979
2025-08-28 22:31:16.979 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0319ms às 22:31:16.979
2025-08-28 22:31:16.979 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 204.1794ms - TRUE INSTANT execution
2025-08-28 22:31:16.979 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:16.979 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:16.979 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:31:16.979 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 204.1633ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:31:16.979 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 6
2025-08-28 22:31:17.226 -03:00 [INF] Buy response processed: Contract 292563316408, Type: Unknown, Stake: 34.31
2025-08-28 22:31:17.227 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563316408, Type: Unknown
2025-08-28 22:31:22.887 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563316408 detectado como finalizado às 22:31:22.887
2025-08-28 22:31:22.887 -03:00 [INF] Contract 292563316408 finished: Profit=32.7, Win=True
2025-08-28 22:31:22.887 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:31:22.887
2025-08-28 22:31:22.887 -03:00 [INF] [TIMING] Contract WIN at 22:31:22.887 - calling OnContractWin
2025-08-28 22:31:22.887 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:31:22.887 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:31:22.887 -03:00 [INF] Profit Table updated for contract 292563316408: Profit=32.7, ExitPrice=6084.479
2025-08-28 22:31:22.887 -03:00 [INF] [TIMING] ContractResult event concluído às 22:31:22.887 (duração: 0.3007ms)
2025-08-28 22:33:36.489 -03:00 [INF] [DEBUG] Contrato comprado: 292563402548, subscrevendo para atualizações
2025-08-28 22:33:36.489 -03:00 [INF] Compra executada com sucesso. ContractId: 292563402548, TransactionId: 582771291488
2025-08-28 22:33:36.489 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:33:36.489
2025-08-28 22:33:36.489 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:33:36.489
2025-08-28 22:33:36.489 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:33:36.739 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:36.739 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:36.759 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:36.759 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:36.759 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:36.760 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 270.834ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:33:36.760 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:33:36.760 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 271.0091ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:33:36.760 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:33:36.760 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:33:36.760 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:33:42.884 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563402548 detectado como finalizado às 22:33:42.884
2025-08-28 22:33:42.884 -03:00 [INF] Contract 292563402548 finished: Profit=-0.4, Win=False
2025-08-28 22:33:42.884 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:33:42.884
2025-08-28 22:33:42.884 -03:00 [INF] [TIMING] Contract LOSS at 22:33:42.884 - ZERO-DELAY EXECUTION
2025-08-28 22:33:42.884 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0269ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:33:42.884 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0354ms. Level: 1
2025-08-28 22:33:42.885 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:33:42.885
2025-08-28 22:33:42.885 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 1.0184ms
2025-08-28 22:33:42.885 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 1.0322ms
2025-08-28 22:33:42.885 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:33:42.885
2025-08-28 22:33:42.885 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:33:42.885 -03:00 [INF] Profit Table updated for contract 292563402548: Profit=-0.4, ExitPrice=6084.218
2025-08-28 22:33:42.885 -03:00 [INF] [TIMING] ContractResult event concluído às 22:33:42.885 (duração: 1.4538ms)
2025-08-28 22:33:43.074 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:43.075 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:43.076 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:43.077 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 191.8873ms às 22:33:43.077
2025-08-28 22:33:43.077 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0647ms às 22:33:43.077
2025-08-28 22:33:43.077 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 191.952ms - TRUE INSTANT execution
2025-08-28 22:33:43.078 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:43.089 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:43.089 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 204.1408ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:33:43.089 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:33:43.298 -03:00 [INF] Buy response processed: Contract 292563406648, Type: Unknown, Stake: 0.84
2025-08-28 22:33:43.300 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563406648, Type: Unknown
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563406648 detectado como finalizado às 22:33:46.778
2025-08-28 22:33:46.779 -03:00 [INF] Contract 292563406648 finished: Profit=-0.84, Win=False
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:33:46.779
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] Contract LOSS at 22:33:46.779 - ZERO-DELAY EXECUTION
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.4916ms. Level: 1 → 2, Stake: 1.76
2025-08-28 22:33:46.779 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.5231ms. Level: 2
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:33:46.779
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.5458ms
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.5555ms
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:33:46.779
2025-08-28 22:33:46.779 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:33:46.779 -03:00 [INF] Profit Table updated for contract 292563406648: Profit=-0.84, ExitPrice=6083.968
2025-08-28 22:33:46.779 -03:00 [INF] [TIMING] ContractResult event concluído às 22:33:46.779 (duração: 0.7758ms)
2025-08-28 22:33:46.979 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:46.980 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:46.980 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 200.7167ms às 22:33:46.980
2025-08-28 22:33:46.980 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0527ms às 22:33:46.980
2025-08-28 22:33:46.980 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 200.7694ms - TRUE INSTANT execution
2025-08-28 22:33:46.991 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:46.991 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:46.992 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:33:46.992 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 212.4938ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:33:46.992 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-08-28 22:33:47.201 -03:00 [INF] Buy response processed: Contract 292563408628, Type: Unknown, Stake: 1.76
2025-08-28 22:33:47.201 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563408628, Type: Unknown
2025-08-28 22:33:52.865 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563408628 detectado como finalizado às 22:33:52.865
2025-08-28 22:33:52.865 -03:00 [INF] Contract 292563408628 finished: Profit=1.68, Win=True
2025-08-28 22:33:52.865 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:33:52.865
2025-08-28 22:33:52.865 -03:00 [INF] [TIMING] Contract WIN at 22:33:52.865 - calling OnContractWin
2025-08-28 22:33:52.865 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:33:52.865 -03:00 [INF] Profit Table updated for contract 292563408628: Profit=1.68, ExitPrice=6084.055
2025-08-28 22:33:52.865 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:33:52.865 -03:00 [INF] [TIMING] ContractResult event concluído às 22:33:52.865 (duração: 0.2209ms)
2025-08-28 22:34:10.518 -03:00 [INF] [DEBUG] Contrato comprado: 292563423568, subscrevendo para atualizações
2025-08-28 22:34:10.518 -03:00 [INF] Compra executada com sucesso. ContractId: 292563423568, TransactionId: 582771332988
2025-08-28 22:34:10.519 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:34:10.519
2025-08-28 22:34:10.519 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:34:10.519
2025-08-28 22:34:10.519 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:34:10.728 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:34:10.729 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:34:10.729 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:34:10.734 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:34:10.734 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass256_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1368
2025-08-28 22:34:10.734 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 215.8778ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:34:10.735 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:34:10.735 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 215.9695ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:34:10.735 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:34:10.735 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:34:10.735 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:34:16.790 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563423568 detectado como finalizado às 22:34:16.790
2025-08-28 22:34:16.790 -03:00 [INF] Contract 292563423568 finished: Profit=0.36, Win=True
2025-08-28 22:34:16.790 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:34:16.790
2025-08-28 22:34:16.790 -03:00 [INF] [TIMING] Contract WIN at 22:34:16.790 - calling OnContractWin
2025-08-28 22:34:16.791 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:34:16.791 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.40
2025-08-28 22:34:16.791 -03:00 [INF] Profit Table updated for contract 292563423568: Profit=0.36, ExitPrice=6084.693
2025-08-28 22:34:16.791 -03:00 [INF] [TIMING] ContractResult event concluído às 22:34:16.791 (duração: 0.4287ms)
2025-08-28 22:44:09.904 -03:00 [INF] Application is shutting down...
2025-08-28 22:44:31.795 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-28 22:44:31.821 -03:00 [INF] Hosting environment: Production
2025-08-28 22:44:31.822 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-28 22:44:32.234 -03:00 [INF] Conectando à API Deriv...
2025-08-28 22:44:37.936 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-28 22:44:38.593 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-28 22:44:38.596 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-28 22:44:38.861 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-28 22:44:38.861 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-28 22:44:39.190 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-28 22:44:39.191 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-28 22:44:39.192 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-28 22:44:39.192 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-28 22:44:39.192 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-28 22:44:39.192 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-28 22:44:39.192 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-28 22:44:39.192 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-28 22:44:39.192 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-28 22:44:39.196 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-28 22:44:39.305 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-28 22:45:03.171 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:45:03.172 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-08-28 22:45:03.179 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-28 22:45:05.700 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:45:05.700 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-08-28 22:45:07.885 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:45:07.885 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-08-28 22:45:08.174 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:45:08.174 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-08-28 22:45:08.283 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:45:08.283 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.4
2025-08-28 22:45:58.780 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-08-28 22:45:58.782 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:45:58.781
2025-08-28 22:45:58.782 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:45:59.012 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:45:59.021 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:45:59.022 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:45:59.022 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:45:59.022 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:45:59.023 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 240.4615ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:45:59.023 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:45:59.023 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-08-28 22:46:03.766 -03:00 [INF] [DEBUG] Contrato comprado: 292563888768, subscrevendo para atualizações
2025-08-28 22:46:03.768 -03:00 [INF] Compra executada com sucesso. ContractId: 292563888768, TransactionId: 582772261908
2025-08-28 22:46:03.771 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set - ID: 292563888768, Type: Higher, Entry: 6086.99, Duration: 1 ticks
2025-08-28 22:46:03.771 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:46:03.771
2025-08-28 22:46:03.771 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:46:03.771
2025-08-28 22:46:03.771 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:46:04.278 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:04.279 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:04.365 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:04.444 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:04.444 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:04.444 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 673.5003ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:46:04.444 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:46:04.445 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 673.7688ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:46:04.445 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:46:04.445 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:46:04.445 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.4
2025-08-28 22:46:08.893 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563888768 detectado como finalizado às 22:46:08.893
2025-08-28 22:46:08.893 -03:00 [INF] Contract 292563888768 finished: Profit=0.36, Win=True
2025-08-28 22:46:08.893 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:46:08.893
2025-08-28 22:46:08.894 -03:00 [INF] [TIMING] Contract WIN at 22:46:08.894 - calling OnContractWin
2025-08-28 22:46:08.894 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:46:08.894 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-08-28 22:46:08.895 -03:00 [INF] Profit Table updated for contract 292563888768: Profit=0.36, ExitPrice=6087.073
2025-08-28 22:46:08.895 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292563888768
2025-08-28 22:46:08.895 -03:00 [INF] [TIMING] ContractResult event concluído às 22:46:08.895 (duração: 1.5925ms)
2025-08-28 22:46:12.916 -03:00 [INF] [DEBUG] Contrato comprado: 292563894968, subscrevendo para atualizações
2025-08-28 22:46:12.916 -03:00 [INF] Compra executada com sucesso. ContractId: 292563894968, TransactionId: 582772274348
2025-08-28 22:46:12.916 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set - ID: 292563894968, Type: Higher, Entry: 6086.797, Duration: 1 ticks
2025-08-28 22:46:12.916 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:46:12.916
2025-08-28 22:46:12.916 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:46:12.916
2025-08-28 22:46:12.916 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:46:13.123 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:13.123 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:13.137 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:13.137 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:13.141 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:13.142 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 225.9329ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:46:13.142 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:46:13.142 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 226.1276ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:46:13.142 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:46:13.142 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:46:13.142 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-08-28 22:46:18.680 -03:00 [INF] [FAST MARTINGALE] Anticipated LOSS detected at 22:46:18.680 - Entry: 6086.797, Current: 6086.543, Contract: Higher
2025-08-28 22:46:18.681 -03:00 [INF] Profit Table updated with anticipated exit - Contract: 292563894968, Exit Price: 6086.543
2025-08-28 22:46:18.681 -03:00 [INF] [FAST MARTINGALE] Executing anticipated buy at 22:46:18.681 with exit price 6086.543 as new entry spot
2025-08-28 22:46:18.683 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.189ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:46:18.684 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.2732ms. Level: 1
2025-08-28 22:46:18.686 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:46:18.685
2025-08-28 22:46:18.686 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 2.3327ms
2025-08-28 22:46:18.686 -03:00 [INF] [FAST MARTINGALE] Anticipated buy executed in 4.4538ms
2025-08-28 22:46:18.686 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:46:18.686
2025-08-28 22:46:18.686 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:46:18.937 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563894968 detectado como finalizado às 22:46:18.937
2025-08-28 22:46:18.938 -03:00 [INF] Contract 292563894968 finished: Profit=0.36, Win=True
2025-08-28 22:46:18.938 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:46:18.938
2025-08-28 22:46:18.938 -03:00 [INF] [TIMING] Contract WIN at 22:46:18.938 - calling OnContractWin
2025-08-28 22:46:18.938 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:46:18.938 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-08-28 22:46:18.938 -03:00 [INF] Profit Table updated for contract 292563894968: Profit=0.36, ExitPrice=6086.844
2025-08-28 22:46:18.938 -03:00 [INF] [TIMING] ContractResult event concluído às 22:46:18.938 (duração: 0.2349ms)
2025-08-28 22:46:18.968 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:18.968 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:18.969 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:18.970 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:18.980 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:18.981 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 294.6423ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:46:18.981 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:46:18.981 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 295.8793ms às 22:46:18.981
2025-08-28 22:46:18.981 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0604ms às 22:46:18.981
2025-08-28 22:46:18.981 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 295.9397ms - TRUE INSTANT execution
2025-08-28 22:46:19.223 -03:00 [INF] Buy response processed: Contract 292563899088, Type: Unknown, Stake: 0.84
2025-08-28 22:46:19.224 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set from purchase - ID: 292563899088, Type: Unknown, Entry: 6086.543, Duration: 1 ticks
2025-08-28 22:46:19.224 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563899088, Type: Unknown
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563899088 detectado como finalizado às 22:46:24.912
2025-08-28 22:46:24.912 -03:00 [INF] Contract 292563899088 finished: Profit=-0.84, Win=False
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:46:24.912
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] Contract LOSS at 22:46:24.912 - ZERO-DELAY EXECUTION
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0469ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:46:24.912 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0606ms. Level: 1
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:46:24.912
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1541ms
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1656ms
2025-08-28 22:46:24.912 -03:00 [INF] Profit Table updated for contract 292563899088: Profit=-0.84, ExitPrice=6086.173
2025-08-28 22:46:24.912 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292563899088
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] ContractResult event concluído às 22:46:24.912 (duração: 0.3412ms)
2025-08-28 22:46:24.912 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:46:24.912
2025-08-28 22:46:24.912 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:46:25.128 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:25.128 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:25.128 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:25.129 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 216.517ms às 22:46:25.129
2025-08-28 22:46:25.129 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0426ms às 22:46:25.129
2025-08-28 22:46:25.129 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 216.5596ms - TRUE INSTANT execution
2025-08-28 22:46:25.129 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:25.129 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:25.129 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 217.0804ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:46:25.129 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:46:25.411 -03:00 [INF] Buy response processed: Contract 292563903228, Type: Unknown, Stake: 0.84
2025-08-28 22:46:25.414 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set from purchase - ID: 292563903228, Type: Unknown, Entry: 6086.363, Duration: 1 ticks
2025-08-28 22:46:25.415 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563903228, Type: Unknown
2025-08-28 22:46:30.839 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563903228 detectado como finalizado às 22:46:30.839
2025-08-28 22:46:30.839 -03:00 [INF] Contract 292563903228 finished: Profit=-0.84, Win=False
2025-08-28 22:46:30.839 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:46:30.839
2025-08-28 22:46:30.839 -03:00 [INF] [TIMING] Contract LOSS at 22:46:30.839 - ZERO-DELAY EXECUTION
2025-08-28 22:46:30.840 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.7454ms. Level: 1 → 2, Stake: 1.76
2025-08-28 22:46:30.840 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.8497ms. Level: 2
2025-08-28 22:46:30.840 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:46:30.840
2025-08-28 22:46:30.840 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.8995ms
2025-08-28 22:46:30.840 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.9145ms
2025-08-28 22:46:30.840 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:46:30.840
2025-08-28 22:46:30.840 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:46:30.840 -03:00 [INF] Profit Table updated for contract 292563903228: Profit=-0.84, ExitPrice=6086.183
2025-08-28 22:46:30.840 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292563903228
2025-08-28 22:46:30.840 -03:00 [INF] [TIMING] ContractResult event concluído às 22:46:30.840 (duração: 1.551ms)
2025-08-28 22:46:31.035 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:31.035 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 195.6749ms às 22:46:31.035
2025-08-28 22:46:31.036 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0819ms às 22:46:31.036
2025-08-28 22:46:31.036 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 195.7568ms - TRUE INSTANT execution
2025-08-28 22:46:31.057 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:31.057 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:31.057 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:31.057 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:46:31.058 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 217.6435ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:46:31.058 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-08-28 22:46:31.291 -03:00 [INF] Buy response processed: Contract 292563907068, Type: Unknown, Stake: 1.76
2025-08-28 22:46:31.292 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set from purchase - ID: 292563907068, Type: Unknown, Entry: 6086.305, Duration: 1 ticks
2025-08-28 22:46:31.292 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563907068, Type: Unknown
2025-08-28 22:46:36.806 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563907068 detectado como finalizado às 22:46:36.805
2025-08-28 22:46:36.807 -03:00 [INF] Contract 292563907068 finished: Profit=1.68, Win=True
2025-08-28 22:46:36.807 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:46:36.807
2025-08-28 22:46:36.807 -03:00 [INF] [TIMING] Contract WIN at 22:46:36.807 - calling OnContractWin
2025-08-28 22:46:36.809 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:46:36.809 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-08-28 22:46:36.810 -03:00 [INF] Profit Table updated for contract 292563907068: Profit=1.68, ExitPrice=6086.383
2025-08-28 22:46:36.810 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292563907068
2025-08-28 22:46:36.810 -03:00 [INF] [TIMING] ContractResult event concluído às 22:46:36.810 (duração: 3.2468ms)
2025-08-28 22:48:38.142 -03:00 [INF] [DEBUG] Contrato comprado: 292563994108, subscrevendo para atualizações
2025-08-28 22:48:38.142 -03:00 [INF] Compra executada com sucesso. ContractId: 292563994108, TransactionId: 582772470908
2025-08-28 22:48:38.142 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set - ID: 292563994108, Type: Higher, Entry: 6083.867, Duration: 1 ticks
2025-08-28 22:48:38.142 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às 22:48:38.142
2025-08-28 22:48:38.142 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:48:38.142
2025-08-28 22:48:38.142 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:48:38.336 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:38.337 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:38.337 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:38.337 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:38.374 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:38.375 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 232.7814ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:48:38.375 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-28 22:48:38.375 -03:00 [INF] [TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em 232.8964ms - propostas GARANTIDAMENTE prontas
2025-08-28 22:48:38.375 -03:00 [INF] [DEBUG] HOT POOL STATUS: 0 propostas prontas nos níveis: []
2025-08-28 22:48:38.375 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:48:38.375 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-08-28 22:48:42.961 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563994108 detectado como finalizado às 22:48:42.961
2025-08-28 22:48:42.961 -03:00 [INF] Contract 292563994108 finished: Profit=-0.4, Win=False
2025-08-28 22:48:42.961 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:48:42.961
2025-08-28 22:48:42.961 -03:00 [INF] [TIMING] Contract LOSS at 22:48:42.961 - ZERO-DELAY EXECUTION
2025-08-28 22:48:42.961 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.054ms. Level: 0 → 1, Stake: 0.84
2025-08-28 22:48:42.961 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0689ms. Level: 1
2025-08-28 22:48:42.961 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:48:42.961
2025-08-28 22:48:42.961 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1151ms
2025-08-28 22:48:42.961 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.1356ms
2025-08-28 22:48:42.961 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:48:42.961
2025-08-28 22:48:42.962 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:48:42.981 -03:00 [INF] Profit Table updated for contract 292563994108: Profit=-0.4, ExitPrice=6084.033
2025-08-28 22:48:42.981 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292563994108
2025-08-28 22:48:42.982 -03:00 [INF] [TIMING] ContractResult event concluído às 22:48:42.982 (duração: 20.4541ms)
2025-08-28 22:48:43.223 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:43.224 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:43.224 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:43.224 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 262.8926ms às 22:48:43.224
2025-08-28 22:48:43.224 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0607ms às 22:48:43.224
2025-08-28 22:48:43.224 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 262.9533ms - TRUE INSTANT execution
2025-08-28 22:48:43.224 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:43.224 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:43.225 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 263.2123ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:48:43.225 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 1
2025-08-28 22:48:43.457 -03:00 [INF] Buy response processed: Contract 292563998328, Type: Unknown, Stake: 0.84
2025-08-28 22:48:43.457 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set from purchase - ID: 292563998328, Type: Unknown, Entry: 6084.042, Duration: 1 ticks
2025-08-28 22:48:43.457 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292563998328, Type: Unknown
2025-08-28 22:48:48.887 -03:00 [INF] [TIMING] FALLBACK - Contrato 292563998328 detectado como finalizado às 22:48:48.887
2025-08-28 22:48:48.887 -03:00 [INF] Contract 292563998328 finished: Profit=-0.84, Win=False
2025-08-28 22:48:48.887 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:48:48.887
2025-08-28 22:48:48.887 -03:00 [INF] [TIMING] Contract LOSS at 22:48:48.887 - ZERO-DELAY EXECUTION
2025-08-28 22:48:48.887 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0978ms. Level: 1 → 2, Stake: 1.76
2025-08-28 22:48:48.887 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.1446ms. Level: 2
2025-08-28 22:48:48.887 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:48:48.887
2025-08-28 22:48:48.887 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.1969ms
2025-08-28 22:48:48.887 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.2192ms
2025-08-28 22:48:48.888 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:48:48.888
2025-08-28 22:48:48.888 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:48:48.888 -03:00 [INF] Profit Table updated for contract 292563998328: Profit=-0.84, ExitPrice=6083.937
2025-08-28 22:48:48.888 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292563998328
2025-08-28 22:48:48.888 -03:00 [INF] [TIMING] ContractResult event concluído às 22:48:48.888 (duração: 0.7082ms)
2025-08-28 22:48:49.097 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:49.114 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:49.114 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:49.114 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:49.115 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 227.1926ms às 22:48:49.115
2025-08-28 22:48:49.115 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0486ms às 22:48:49.115
2025-08-28 22:48:49.115 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 227.2412ms - TRUE INSTANT execution
2025-08-28 22:48:49.148 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:49.148 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 260.8906ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:48:49.148 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 2
2025-08-28 22:48:49.381 -03:00 [INF] Buy response processed: Contract 292564002228, Type: Unknown, Stake: 1.76
2025-08-28 22:48:49.384 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set from purchase - ID: 292564002228, Type: Unknown, Entry: 6083.923, Duration: 1 ticks
2025-08-28 22:48:49.384 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292564002228, Type: Unknown
2025-08-28 22:48:54.817 -03:00 [INF] [TIMING] FALLBACK - Contrato 292564002228 detectado como finalizado às 22:48:54.817
2025-08-28 22:48:54.818 -03:00 [INF] Contract 292564002228 finished: Profit=-1.76, Win=False
2025-08-28 22:48:54.818 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:48:54.818
2025-08-28 22:48:54.818 -03:00 [INF] [TIMING] Contract LOSS at 22:48:54.818 - ZERO-DELAY EXECUTION
2025-08-28 22:48:54.818 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0731ms. Level: 2 → 3, Stake: 3.70
2025-08-28 22:48:54.820 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.1603ms. Level: 3
2025-08-28 22:48:54.820 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:48:54.820
2025-08-28 22:48:54.820 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 2.66ms
2025-08-28 22:48:54.820 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 2.6961ms
2025-08-28 22:48:54.820 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:48:54.820
2025-08-28 22:48:54.821 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:48:54.821 -03:00 [INF] Profit Table updated for contract 292564002228: Profit=-1.76, ExitPrice=6084.122
2025-08-28 22:48:54.821 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292564002228
2025-08-28 22:48:54.821 -03:00 [INF] [TIMING] ContractResult event concluído às 22:48:54.821 (duração: 3.2089ms)
2025-08-28 22:48:55.071 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 250.8628ms às 22:48:55.071
2025-08-28 22:48:55.071 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.2206ms às 22:48:55.071
2025-08-28 22:48:55.071 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 251.0834ms - TRUE INSTANT execution
2025-08-28 22:48:55.072 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:55.072 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:55.073 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:55.073 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:55.073 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:48:55.073 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 252.627ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:48:55.073 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 3
2025-08-28 22:48:55.410 -03:00 [INF] Buy response processed: Contract 292564006268, Type: Unknown, Stake: 3.7
2025-08-28 22:48:55.411 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set from purchase - ID: 292564006268, Type: Unknown, Entry: 6084.09, Duration: 1 ticks
2025-08-28 22:48:55.411 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292564006268, Type: Unknown
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] FALLBACK - Contrato 292564006268 detectado como finalizado às 22:49:00.897
2025-08-28 22:49:00.897 -03:00 [INF] Contract 292564006268 finished: Profit=-3.7, Win=False
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:49:00.897
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] Contract LOSS at 22:49:00.897 - ZERO-DELAY EXECUTION
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.0431ms. Level: 3 → 4, Stake: 7.78
2025-08-28 22:49:00.897 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0567ms. Level: 4
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:49:00.897
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 0.0784ms
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 0.0917ms
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:49:00.897
2025-08-28 22:49:00.897 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:49:00.897 -03:00 [INF] Profit Table updated for contract 292564006268: Profit=-3.7, ExitPrice=6084.22
2025-08-28 22:49:00.897 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292564006268
2025-08-28 22:49:00.897 -03:00 [INF] [TIMING] ContractResult event concluído às 22:49:00.897 (duração: 0.2958ms)
2025-08-28 22:49:01.137 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:01.137 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:01.144 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:01.155 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:01.155 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 258.2222ms às 22:49:01.155
2025-08-28 22:49:01.155 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0606ms às 22:49:01.155
2025-08-28 22:49:01.155 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 258.2828ms - TRUE INSTANT execution
2025-08-28 22:49:01.155 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:01.156 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 258.891ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:49:01.156 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 4
2025-08-28 22:49:01.403 -03:00 [INF] Buy response processed: Contract 292564010188, Type: Unknown, Stake: 7.78
2025-08-28 22:49:01.403 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set from purchase - ID: 292564010188, Type: Unknown, Entry: 6084.133, Duration: 1 ticks
2025-08-28 22:49:01.403 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292564010188, Type: Unknown
2025-08-28 22:49:06.969 -03:00 [INF] [TIMING] FALLBACK - Contrato 292564010188 detectado como finalizado às 22:49:06.969
2025-08-28 22:49:06.969 -03:00 [INF] Contract 292564010188 finished: Profit=-7.78, Win=False
2025-08-28 22:49:06.969 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:49:06.969
2025-08-28 22:49:06.969 -03:00 [INF] [TIMING] Contract LOSS at 22:49:06.969 - ZERO-DELAY EXECUTION
2025-08-28 22:49:06.969 -03:00 [INF] [TIMING] ULTRA-FAST: State updated in 0.058ms. Level: 4 → 5, Stake: 16.34
2025-08-28 22:49:06.969 -03:00 [ERR] [TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in 0.0923ms. Level: 5
2025-08-28 22:49:06.969 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 22:49:06.969
2025-08-28 22:49:06.989 -03:00 [INF] [TIMING] ULTRA-FAST EMERGENCY: Fallback sent in 19.4664ms
2025-08-28 22:49:06.989 -03:00 [INF] [TIMING] ZERO-DELAY: Complete execution in 19.6426ms
2025-08-28 22:49:06.989 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 22:49:06.989
2025-08-28 22:49:06.989 -03:00 [INF] Profit Table updated for contract 292564010188: Profit=-7.78, ExitPrice=6084.088
2025-08-28 22:49:06.989 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292564010188
2025-08-28 22:49:06.989 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-28 22:49:06.989 -03:00 [INF] [TIMING] ContractResult event concluído às 22:49:06.989 (duração: 20.1524ms)
2025-08-28 22:49:07.213 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:07.213 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 243.8337ms às 22:49:07.213
2025-08-28 22:49:07.213 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0621ms às 22:49:07.213
2025-08-28 22:49:07.213 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 243.8958ms - TRUE INSTANT execution
2025-08-28 22:49:07.220 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:07.220 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:07.223 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:07.223 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Stake can not have more than 2 decimal places.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 427
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass262_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1382
2025-08-28 22:49:07.223 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 233.9858ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-28 22:49:07.223 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 5
2025-08-28 22:49:07.476 -03:00 [INF] Buy response processed: Contract 292564015568, Type: Unknown, Stake: 16.34
2025-08-28 22:49:07.478 -03:00 [INF] [FAST MARTINGALE] Active contract tracking set from purchase - ID: 292564015568, Type: Unknown, Entry: 6084.203, Duration: 1 ticks
2025-08-28 22:49:07.478 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 292564015568, Type: Unknown
2025-08-28 22:49:12.842 -03:00 [INF] [TIMING] FALLBACK - Contrato 292564015568 detectado como finalizado às 22:49:12.842
2025-08-28 22:49:12.842 -03:00 [INF] Contract 292564015568 finished: Profit=15.57, Win=True
2025-08-28 22:49:12.842 -03:00 [INF] [TIMING] Disparando ContractResult event às 22:49:12.842
2025-08-28 22:49:12.842 -03:00 [INF] [TIMING] Contract WIN at 22:49:12.842 - calling OnContractWin
2025-08-28 22:49:12.842 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-28 22:49:12.842 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.40
2025-08-28 22:49:12.842 -03:00 [INF] Profit Table updated for contract 292564015568: Profit=15.57, ExitPrice=6084.185
2025-08-28 22:49:12.842 -03:00 [INF] [FAST MARTINGALE] Clearing active contract tracking for finished contract: 292564015568
2025-08-28 22:49:12.842 -03:00 [INF] [TIMING] ContractResult event concluído às 22:49:12.842 (duração: 0.576ms)
