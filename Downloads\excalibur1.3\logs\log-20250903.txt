2025-09-03 20:49:21.060 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-03 20:49:21.083 -03:00 [INF] Hosting environment: Production
2025-09-03 20:49:21.084 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-03 20:49:21.497 -03:00 [INF] Conectando à API Deriv...
2025-09-03 20:49:22.386 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-03 20:49:22.784 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 20:49:22.789 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 20:49:22.808 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 20:49:22.808 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 20:49:23.330 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 20:49:23.332 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 20:49:23.333 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 20:49:23.333 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 20:49:23.333 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 20:49:23.333 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 20:49:23.333 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 20:49:23.333 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 20:49:23.333 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 20:49:23.336 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 20:49:23.444 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 20:49:23.445 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 20:49:38.068 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:38.069 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:49:38.076 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-03 20:49:45.915 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:45.915 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:49:54.419 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:54.420 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:49:54.671 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:54.671 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:49:54.785 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:54.785 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:49:54.983 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:54.983 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 20:49:55.629 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:55.629 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:49:56.060 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:56.060 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:49:56.330 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:57.030 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:49:57.034 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:57.034 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:49:57.053 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:57.053 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:49:57.391 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:57.391 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:49:57.634 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:57.634 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 20:49:58.091 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:58.091 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:49:59.335 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:59.335 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:49:59.696 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:49:59.696 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:50:00.150 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:50:00.150 -03:00 [INF] [DEBUG] Saindo: DurationValue inválida: 0
2025-09-03 20:50:00.411 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:50:00.411 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:50:00.602 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:50:00.602 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 20:50:01.223 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:50:01.223 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:50:01.629 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:50:01.630 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:50:01.782 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:50:01.782 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=PUTE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 20:50:02.130 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:50:02.130 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:50:02.671 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:50:02.671 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:50:23.734 -03:00 [WRN] Contrato PUTE não possui oposto definido
2025-09-03 20:50:23.734 -03:00 [WRN] Este tipo de contrato não suporta modo Dual
2025-09-03 20:50:35.688 -03:00 [WRN] Contrato PUTE não possui oposto definido
2025-09-03 20:50:35.688 -03:00 [WRN] Este tipo de contrato não suporta modo Dual
2025-09-03 20:50:45.069 -03:00 [WRN] Contrato PUTE não possui oposto definido
2025-09-03 20:50:45.069 -03:00 [WRN] Este tipo de contrato não suporta modo Dual
2025-09-03 20:50:49.711 -03:00 [INF] Application is shutting down...
2025-09-03 20:50:50.070 -03:00 [ERR] [TICKS] Error processing tick update
System.NullReferenceException: Object reference not set to an instance of an object.
   at Excalibur.ViewModels.MainViewModel.OnTickReceived(Decimal price, DateTime timestamp) in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 838
   at Excalibur.Services.DerivApiService.ProcessTickUpdate(JsonElement root) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 1089
2025-09-03 20:52:43.738 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-03 20:52:43.764 -03:00 [INF] Hosting environment: Production
2025-09-03 20:52:43.765 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-03 20:52:43.999 -03:00 [INF] Conectando à API Deriv...
2025-09-03 20:52:44.626 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-03 20:52:45.049 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 20:52:45.053 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 20:52:45.074 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 20:52:45.075 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 20:52:45.593 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 20:52:45.593 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 20:52:45.594 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 20:52:45.594 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 20:52:45.594 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 20:52:45.594 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 20:52:45.594 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 20:52:45.594 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 20:52:45.594 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 20:52:45.597 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 20:52:45.687 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 20:52:45.687 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 20:52:45.687 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 20:52:45.687 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 20:52:45.687 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 20:52:45.687 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 20:52:45.688 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 20:52:45.688 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 20:52:45.688 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 20:52:45.688 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 20:53:06.899 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 20:53:06.900 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 20:53:06.907 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-03 20:53:24.199 -03:00 [INF] [BUY] ExecuteBuyCommand iniciado - IsDualEnabled: True
2025-09-03 20:53:24.200 -03:00 [INF] [BUY] Modo Dual detectado - chamando ExecuteDualEntry
2025-09-03 20:53:24.202 -03:00 [INF] [DUAL] ExecuteDualEntry iniciado
2025-09-03 20:53:24.202 -03:00 [INF] [DUAL] Tipo de contrato selecionado: PUTE
2025-09-03 20:53:24.202 -03:00 [WRN] Contrato PUTE não possui oposto definido
2025-09-03 20:53:24.202 -03:00 [WRN] [DUAL] Este tipo de contrato não suporta modo Dual: PUTE
2025-09-03 20:53:46.960 -03:00 [INF] Application is shutting down...
2025-09-03 21:05:15.765 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-03 21:05:15.787 -03:00 [INF] Hosting environment: Production
2025-09-03 21:05:15.787 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-03 21:05:16.017 -03:00 [INF] Conectando à API Deriv...
2025-09-03 21:05:16.707 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-03 21:05:17.101 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 21:05:17.103 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 21:05:17.118 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 21:05:17.118 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 21:05:17.424 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 21:05:17.425 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 21:05:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 21:05:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 21:05:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 21:05:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 21:05:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 21:05:17.426 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 21:05:17.426 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 21:05:17.428 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 21:05:17.554 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 21:05:17.555 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 21:05:56.374 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:05:56.376 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-03 21:05:56.382 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-03 21:06:00.226 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:00.226 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1.00
2025-09-03 21:06:03.480 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:03.480 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=2
2025-09-03 21:06:03.817 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:03.817 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=2
2025-09-03 21:06:04.174 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:04.174 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=2.4
2025-09-03 21:06:04.350 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:04.350 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 21:06:04.669 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:04.669 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=2.4
2025-09-03 21:06:04.828 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:04.829 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 21:06:04.994 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:04.994 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=2
2025-09-03 21:06:05.423 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:05.423 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-03 21:06:06.020 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:06.020 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-03 21:06:06.152 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:06.152 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.3
2025-09-03 21:06:07.280 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:07.280 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-03 21:06:08.671 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:08.671 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.3
2025-09-03 21:06:10.735 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:10.735 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-03 21:06:10.904 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:10.904 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.2
2025-09-03 21:06:12.553 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:12.554 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-03 21:06:13.788 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:13.788 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.3
2025-09-03 21:06:14.240 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:14.241 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-03 21:06:14.978 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:14.978 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.3
2025-09-03 21:06:15.121 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:15.121 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 21:06:15.277 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:15.277 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 21:06:15.450 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:15.450 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 21:06:16.921 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:16.921 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.4
2025-09-03 21:06:17.071 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:17.071 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 21:06:17.190 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:17.190 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-03 21:06:17.493 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:17.493 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1
2025-09-03 21:06:18.891 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:18.891 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-03 21:06:19.153 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:19.153 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-03 21:06:19.427 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:19.427 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.3
2025-09-03 21:06:20.220 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:20.220 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-03 21:06:20.304 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:20.304 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.4
2025-09-03 21:06:21.609 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:21.609 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-03 21:06:21.854 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:21.854 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.3
2025-09-03 21:06:22.924 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:22.924 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-03 21:06:26.320 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:26.320 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.3
2025-09-03 21:06:26.697 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:26.697 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.30
2025-09-03 21:06:28.378 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:28.378 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.3
2025-09-03 21:06:28.552 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:28.552 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-03 21:06:30.935 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:06:30.935 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-03 21:07:05.075 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:05.075
2025-09-03 21:07:05.075 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:05.075
2025-09-03 21:07:05.273 -03:00 [ERR] Erro ao obter hot proposal
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetHotProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 994
2025-09-03 21:07:05.291 -03:00 [INF] [TIMING] INSTANT POOL: Sem proposta disponível, criando nova
2025-09-03 21:07:05.301 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 225.5522ms às 21:07:05.301
2025-09-03 21:07:05.304 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 3.111ms às 21:07:05.304
2025-09-03 21:07:05.304 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 228.6632ms - TRUE INSTANT execution
2025-09-03 21:07:05.503 -03:00 [ERR] [TIMING] INSTANT POOL BUY: Erro após 428.5049ms
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.Services.DerivApiService.<>c__DisplayClass66_0.<<BuyInstantMarketAsync>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 907
2025-09-03 21:07:05.504 -03:00 [INF] [DUAL] Entrada dupla executada - Principal: CALLE (-3.97), Oposto: PUTE (0.35)
2025-09-03 21:07:05.652 -03:00 [INF] Buy response processed: Contract 293149053288, Type: Unknown, Stake: 0.35
2025-09-03 21:07:05.653 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293149053288, Type: Unknown, PurchaseTime: 00:15:17
2025-09-03 21:07:06.219 -03:00 [INF] [DEBUG] entry_tick for 293149053288: epoch=1756944918 -> 00:15:18.000, price=5964.329
2025-09-03 21:07:06.220 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149053288: 5964.329 at 00:15:18
2025-09-03 21:07:06.230 -03:00 [INF] [DEBUG] entry_tick for 293149053288: epoch=1756944918 -> 00:15:18.000, price=5964.329
2025-09-03 21:07:06.230 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149053288: 5964.329 at 00:15:18
2025-09-03 21:07:08.095 -03:00 [INF] [DEBUG] entry_tick for 293149053288: epoch=1756944918 -> 00:15:18.000, price=5964.329
2025-09-03 21:07:08.099 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149053288: 5964.329 at 00:15:18
2025-09-03 21:07:08.100 -03:00 [INF] [DEBUG] entry_tick for 293149053288: epoch=1756944918 -> 00:15:18.000, price=5964.329
2025-09-03 21:07:08.100 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149053288: 5964.329 at 00:15:18
2025-09-03 21:07:10.254 -03:00 [INF] [DEBUG] entry_tick for 293149053288: epoch=1756944918 -> 00:15:18.000, price=5964.329
2025-09-03 21:07:10.254 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149053288: 5964.329 at 00:15:18
2025-09-03 21:07:10.255 -03:00 [INF] [TIMING] FALLBACK - Contrato 293149053288 detectado como finalizado
2025-09-03 21:07:10.255 -03:00 [INF] Contract 293149053288 finished: Profit=0.31, Win=True
2025-09-03 21:07:10.255 -03:00 [INF] [DEBUG] exit_time for 293149053288: sell_time_epoch=1756944920 -> 00:15:20.000
2025-09-03 21:07:10.255 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:07:10.255
2025-09-03 21:07:10.255 -03:00 [INF] [DUAL] Contract result received at 21:07:10.255 - Win: True, Type: CALLE
2025-09-03 21:07:10.256 -03:00 [INF] [DUAL] Lucro atingido com contrato CALLE
2025-09-03 21:07:10.256 -03:00 [INF] [DUAL] Estado resetado
2025-09-03 21:07:10.258 -03:00 [INF] Profit Table updated for contract 293149053288: Profit=0.31, ExitPrice=5964.222, ExitTime=00:15:20
2025-09-03 21:07:10.258 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149053288: 5964.329 at 00:15:18
2025-09-03 21:07:10.258 -03:00 [INF] [TIMING] ContractResult event concluído às 21:07:10.258 (duração: 3.4768ms)
2025-09-03 21:07:19.525 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:19.525
2025-09-03 21:07:19.525 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:19.525
2025-09-03 21:07:19.691 -03:00 [ERR] Erro ao obter hot proposal
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetHotProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 994
2025-09-03 21:07:19.691 -03:00 [INF] [TIMING] INSTANT POOL: Sem proposta disponível, criando nova
2025-09-03 21:07:19.731 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 205.8352ms às 21:07:19.731
2025-09-03 21:07:19.731 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1482ms às 21:07:19.731
2025-09-03 21:07:19.731 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 205.9834ms - TRUE INSTANT execution
2025-09-03 21:07:19.863 -03:00 [ERR] [TIMING] INSTANT POOL BUY: Erro após 337.6193ms
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.Services.DerivApiService.<>c__DisplayClass66_0.<<BuyInstantMarketAsync>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 907
2025-09-03 21:07:19.863 -03:00 [INF] [DUAL] Entrada dupla executada - Principal: CALLE (-3.97), Oposto: PUTE (0.35)
2025-09-03 21:07:20.097 -03:00 [INF] Buy response processed: Contract 293149064628, Type: Unknown, Stake: 0.35
2025-09-03 21:07:20.099 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293149064628, Type: Unknown, PurchaseTime: 00:15:32
2025-09-03 21:07:22.114 -03:00 [INF] [DEBUG] entry_tick for 293149064628: epoch=1756944934 -> 00:15:34.000, price=5964.188
2025-09-03 21:07:22.116 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149064628: 5964.188 at 00:15:34
2025-09-03 21:07:22.116 -03:00 [INF] [DEBUG] entry_tick for 293149064628: epoch=1756944934 -> 00:15:34.000, price=5964.188
2025-09-03 21:07:22.116 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149064628: 5964.188 at 00:15:34
2025-09-03 21:07:24.192 -03:00 [INF] [DEBUG] entry_tick for 293149064628: epoch=1756944934 -> 00:15:34.000, price=5964.188
2025-09-03 21:07:24.192 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149064628: 5964.188 at 00:15:34
2025-09-03 21:07:24.197 -03:00 [INF] [DEBUG] entry_tick for 293149064628: epoch=1756944934 -> 00:15:34.000, price=5964.188
2025-09-03 21:07:24.197 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149064628: 5964.188 at 00:15:34
2025-09-03 21:07:26.278 -03:00 [INF] [DEBUG] entry_tick for 293149064628: epoch=1756944934 -> 00:15:34.000, price=5964.188
2025-09-03 21:07:26.278 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149064628: 5964.188 at 00:15:34
2025-09-03 21:07:26.279 -03:00 [INF] [TIMING] FALLBACK - Contrato 293149064628 detectado como finalizado
2025-09-03 21:07:26.279 -03:00 [INF] Contract 293149064628 finished: Profit=-0.35, Win=False
2025-09-03 21:07:26.279 -03:00 [INF] [DEBUG] exit_time for 293149064628: sell_time_epoch=1756944937 -> 00:15:37.000
2025-09-03 21:07:26.279 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:07:26.279
2025-09-03 21:07:26.279 -03:00 [INF] [DUAL] Contract result received at 21:07:26.279 - Win: False, Type: CALLE
2025-09-03 21:07:26.279 -03:00 [INF] Profit Table updated for contract 293149064628: Profit=-0.35, ExitPrice=5964.256, ExitTime=00:15:37
2025-09-03 21:07:26.279 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149064628: 5964.188 at 00:15:34
2025-09-03 21:07:26.279 -03:00 [INF] [TIMING] ContractResult event concluído às 21:07:26.279 (duração: 0.514ms)
2025-09-03 21:07:26.495 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:26.495
2025-09-03 21:07:26.495 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:26.495
2025-09-03 21:07:26.665 -03:00 [ERR] Erro ao obter hot proposal
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetHotProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 994
2025-09-03 21:07:26.665 -03:00 [INF] [TIMING] INSTANT POOL: Sem proposta disponível, criando nova
2025-09-03 21:07:26.704 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 208.9236ms às 21:07:26.704
2025-09-03 21:07:26.704 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1511ms às 21:07:26.704
2025-09-03 21:07:26.704 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 209.0747ms - TRUE INSTANT execution
2025-09-03 21:07:26.835 -03:00 [ERR] [TIMING] INSTANT POOL BUY: Erro após 340.3492ms
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.Services.DerivApiService.<>c__DisplayClass66_0.<<BuyInstantMarketAsync>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 907
2025-09-03 21:07:26.836 -03:00 [INF] [DUAL] Entrada dupla executada - Principal: CALLE (-3.97), Oposto: PUTE (0.35)
2025-09-03 21:07:27.017 -03:00 [INF] Buy response processed: Contract 293149070668, Type: Unknown, Stake: 0.35
2025-09-03 21:07:27.018 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293149070668, Type: Unknown, PurchaseTime: 00:15:39
2025-09-03 21:07:28.108 -03:00 [INF] [DEBUG] entry_tick for 293149070668: epoch=1756944940 -> 00:15:40.000, price=5964.008
2025-09-03 21:07:28.109 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149070668: 5964.008 at 00:15:40
2025-09-03 21:07:28.123 -03:00 [INF] [DEBUG] entry_tick for 293149070668: epoch=1756944940 -> 00:15:40.000, price=5964.008
2025-09-03 21:07:28.123 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149070668: 5964.008 at 00:15:40
2025-09-03 21:07:30.097 -03:00 [INF] [DEBUG] entry_tick for 293149070668: epoch=1756944940 -> 00:15:40.000, price=5964.008
2025-09-03 21:07:30.101 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149070668: 5964.008 at 00:15:40
2025-09-03 21:07:30.101 -03:00 [INF] [DEBUG] entry_tick for 293149070668: epoch=1756944940 -> 00:15:40.000, price=5964.008
2025-09-03 21:07:30.102 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149070668: 5964.008 at 00:15:40
2025-09-03 21:07:32.186 -03:00 [INF] [DEBUG] entry_tick for 293149070668: epoch=1756944940 -> 00:15:40.000, price=5964.008
2025-09-03 21:07:32.186 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149070668: 5964.008 at 00:15:40
2025-09-03 21:07:32.186 -03:00 [INF] [TIMING] FALLBACK - Contrato 293149070668 detectado como finalizado
2025-09-03 21:07:32.186 -03:00 [INF] Contract 293149070668 finished: Profit=-0.35, Win=False
2025-09-03 21:07:32.186 -03:00 [INF] [DEBUG] exit_time for 293149070668: sell_time_epoch=1756944942 -> 00:15:42.000
2025-09-03 21:07:32.186 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:07:32.186
2025-09-03 21:07:32.186 -03:00 [INF] [DUAL] Contract result received at 21:07:32.186 - Win: False, Type: CALLE
2025-09-03 21:07:32.186 -03:00 [INF] Profit Table updated for contract 293149070668: Profit=-0.35, ExitPrice=5964.179, ExitTime=00:15:42
2025-09-03 21:07:32.186 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149070668: 5964.008 at 00:15:40
2025-09-03 21:07:32.186 -03:00 [INF] [TIMING] ContractResult event concluído às 21:07:32.186 (duração: 0.4067ms)
2025-09-03 21:07:32.422 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:32.422
2025-09-03 21:07:32.422 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:32.422
2025-09-03 21:07:32.599 -03:00 [ERR] Erro ao obter hot proposal
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetHotProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 994
2025-09-03 21:07:32.599 -03:00 [INF] [TIMING] INSTANT POOL: Sem proposta disponível, criando nova
2025-09-03 21:07:32.628 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 206.1938ms às 21:07:32.628
2025-09-03 21:07:32.628 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.1024ms às 21:07:32.628
2025-09-03 21:07:32.628 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 206.2962ms - TRUE INSTANT execution
2025-09-03 21:07:32.782 -03:00 [ERR] [TIMING] INSTANT POOL BUY: Erro após 359.6533ms
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.Services.DerivApiService.<>c__DisplayClass66_0.<<BuyInstantMarketAsync>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 907
2025-09-03 21:07:32.788 -03:00 [INF] [DUAL] Entrada dupla executada - Principal: CALLE (-3.97), Oposto: PUTE (0.35)
2025-09-03 21:07:32.905 -03:00 [INF] Buy response processed: Contract 293149075368, Type: Unknown, Stake: 0.35
2025-09-03 21:07:32.906 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293149075368, Type: Unknown, PurchaseTime: 00:15:45
2025-09-03 21:07:34.079 -03:00 [INF] [DEBUG] entry_tick for 293149075368: epoch=1756944946 -> 00:15:46.000, price=5964.002
2025-09-03 21:07:34.086 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149075368: 5964.002 at 00:15:46
2025-09-03 21:07:34.086 -03:00 [INF] [DEBUG] entry_tick for 293149075368: epoch=1756944946 -> 00:15:46.000, price=5964.002
2025-09-03 21:07:34.087 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149075368: 5964.002 at 00:15:46
2025-09-03 21:07:36.077 -03:00 [INF] [DEBUG] entry_tick for 293149075368: epoch=1756944946 -> 00:15:46.000, price=5964.002
2025-09-03 21:07:36.077 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149075368: 5964.002 at 00:15:46
2025-09-03 21:07:36.087 -03:00 [INF] [DEBUG] entry_tick for 293149075368: epoch=1756944946 -> 00:15:46.000, price=5964.002
2025-09-03 21:07:36.089 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149075368: 5964.002 at 00:15:46
2025-09-03 21:07:38.082 -03:00 [INF] [DEBUG] entry_tick for 293149075368: epoch=1756944946 -> 00:15:46.000, price=5964.002
2025-09-03 21:07:38.082 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149075368: 5964.002 at 00:15:46
2025-09-03 21:07:38.082 -03:00 [INF] [TIMING] FALLBACK - Contrato 293149075368 detectado como finalizado
2025-09-03 21:07:38.083 -03:00 [INF] Contract 293149075368 finished: Profit=-0.35, Win=False
2025-09-03 21:07:38.083 -03:00 [INF] [DEBUG] exit_time for 293149075368: sell_time_epoch=1756944948 -> 00:15:48.000
2025-09-03 21:07:38.083 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:07:38.083
2025-09-03 21:07:38.083 -03:00 [INF] [DUAL] Contract result received at 21:07:38.083 - Win: False, Type: CALLE
2025-09-03 21:07:38.088 -03:00 [INF] Profit Table updated for contract 293149075368: Profit=-0.35, ExitPrice=5964.033, ExitTime=00:15:48
2025-09-03 21:07:38.089 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149075368: 5964.002 at 00:15:46
2025-09-03 21:07:38.089 -03:00 [INF] [TIMING] ContractResult event concluído às 21:07:38.089 (duração: 6.8483ms)
2025-09-03 21:07:38.319 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:38.319
2025-09-03 21:07:38.319 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 21:07:38.319
2025-09-03 21:07:38.498 -03:00 [ERR] Erro ao obter hot proposal
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetHotProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 994
2025-09-03 21:07:38.499 -03:00 [INF] [TIMING] INSTANT POOL: Sem proposta disponível, criando nova
2025-09-03 21:07:38.533 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 214.2572ms às 21:07:38.533
2025-09-03 21:07:38.533 -03:00 [INF] [TIMING] INSTANT POOL: Compra enviada em 0.0851ms às 21:07:38.533
2025-09-03 21:07:38.533 -03:00 [INF] [TIMING] INSTANT POOL BUY: COMPLETED in 214.3423ms - TRUE INSTANT execution
2025-09-03 21:07:38.665 -03:00 [ERR] [TIMING] INSTANT POOL BUY: Erro após 345.7168ms
System.Exception: Input validation failed: amount
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 358
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 434
   at Excalibur.Services.DerivApiService.<>c__DisplayClass66_0.<<BuyInstantMarketAsync>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 907
2025-09-03 21:07:38.665 -03:00 [INF] [DUAL] Entrada dupla executada - Principal: CALLE (-3.97), Oposto: PUTE (0.35)
2025-09-03 21:07:38.806 -03:00 [INF] Buy response processed: Contract 293149080288, Type: Unknown, Stake: 0.35
2025-09-03 21:07:38.806 -03:00 [INF] Profit Table entry added for automatic purchase - Contract: 293149080288, Type: Unknown, PurchaseTime: 00:15:50
2025-09-03 21:07:40.088 -03:00 [INF] [DEBUG] entry_tick for 293149080288: epoch=1756944952 -> 00:15:52.000, price=5964.142
2025-09-03 21:07:40.092 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149080288: 5964.142 at 00:15:52
2025-09-03 21:07:40.104 -03:00 [INF] [DEBUG] entry_tick for 293149080288: epoch=1756944952 -> 00:15:52.000, price=5964.142
2025-09-03 21:07:40.104 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149080288: 5964.142 at 00:15:52
2025-09-03 21:07:42.112 -03:00 [INF] [DEBUG] entry_tick for 293149080288: epoch=1756944952 -> 00:15:52.000, price=5964.142
2025-09-03 21:07:42.112 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149080288: 5964.142 at 00:15:52
2025-09-03 21:07:42.112 -03:00 [INF] [DEBUG] entry_tick for 293149080288: epoch=1756944952 -> 00:15:52.000, price=5964.142
2025-09-03 21:07:42.112 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149080288: 5964.142 at 00:15:52
2025-09-03 21:07:44.160 -03:00 [INF] [DEBUG] entry_tick for 293149080288: epoch=1756944952 -> 00:15:52.000, price=5964.142
2025-09-03 21:07:44.160 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149080288: 5964.142 at 00:15:52
2025-09-03 21:07:44.160 -03:00 [INF] [TIMING] FALLBACK - Contrato 293149080288 detectado como finalizado
2025-09-03 21:07:44.160 -03:00 [INF] Contract 293149080288 finished: Profit=0.31, Win=True
2025-09-03 21:07:44.160 -03:00 [INF] [DEBUG] exit_time for 293149080288: sell_time_epoch=1756944954 -> 00:15:54.000
2025-09-03 21:07:44.160 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:07:44.160
2025-09-03 21:07:44.160 -03:00 [INF] [DUAL] Contract result received at 21:07:44.160 - Win: True, Type: CALLE
2025-09-03 21:07:44.160 -03:00 [INF] [DUAL] Lucro atingido com contrato CALLE
2025-09-03 21:07:44.160 -03:00 [INF] [DUAL] Estado resetado
2025-09-03 21:07:44.160 -03:00 [INF] Profit Table updated for contract 293149080288: Profit=0.31, ExitPrice=5964.051, ExitTime=00:15:54
2025-09-03 21:07:44.160 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293149080288: 5964.142 at 00:15:52
2025-09-03 21:07:44.160 -03:00 [INF] [TIMING] ContractResult event concluído às 21:07:44.160 (duração: 0.2242ms)
2025-09-03 21:08:06.882 -03:00 [INF] Application is shutting down...
2025-09-03 21:10:35.558 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-03 21:10:35.585 -03:00 [INF] Hosting environment: Production
2025-09-03 21:10:35.586 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-03 21:10:35.838 -03:00 [INF] Conectando à API Deriv...
2025-09-03 21:10:36.534 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-03 21:10:36.905 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 21:10:36.907 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 21:10:36.923 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 21:10:36.923 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 21:10:37.374 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 21:10:37.375 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 21:10:37.375 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 21:10:37.376 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 21:10:37.376 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 21:10:37.376 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 21:10:37.376 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 21:10:37.376 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 21:10:37.376 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 21:10:37.377 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 21:10:37.490 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 21:10:42.525 -03:00 [INF] Application is shutting down...
2025-09-03 21:24:30.080 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-03 21:24:30.101 -03:00 [INF] Hosting environment: Production
2025-09-03 21:24:30.101 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-03 21:24:30.331 -03:00 [INF] Conectando à API Deriv...
2025-09-03 21:24:31.039 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-03 21:24:31.429 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 21:24:31.432 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 21:24:31.449 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 21:24:31.449 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 21:24:31.901 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 21:24:31.902 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 21:24:31.902 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 21:24:31.902 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 21:24:31.902 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 21:24:31.902 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 21:24:31.902 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 21:24:31.902 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 21:24:31.903 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 21:24:31.904 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 21:24:31.927 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 21:24:31.928 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 21:25:08.550 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:25:08.551 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 21:25:08.558 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-03 21:25:10.982 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:25:10.982 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 21:25:25.283 -03:00 [INF] [DEBUG] Contrato comprado: 293150028508, subscrevendo para atualizações
2025-09-03 21:25:25.283 -03:00 [INF] Compra executada com sucesso. ContractId: 293150028508, TransactionId: 583912912448
2025-09-03 21:25:25.293 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:25:25.294 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 21:25:26.229 -03:00 [INF] [DEBUG] entry_tick for 293150028508: epoch=1756946018 -> 00:33:38.000, price=5958.888
2025-09-03 21:25:26.231 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293150028508: 5958.888 at 00:33:38
2025-09-03 21:25:26.231 -03:00 [INF] [DEBUG] entry_tick for 293150028508: epoch=1756946018 -> 00:33:38.000, price=5958.888
2025-09-03 21:25:26.244 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293150028508: 5958.888 at 00:33:38
2025-09-03 21:25:28.299 -03:00 [INF] [DEBUG] entry_tick for 293150028508: epoch=1756946018 -> 00:33:38.000, price=5958.888
2025-09-03 21:25:28.300 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293150028508: 5958.888 at 00:33:38
2025-09-03 21:25:28.312 -03:00 [INF] [DEBUG] entry_tick for 293150028508: epoch=1756946018 -> 00:33:38.000, price=5958.888
2025-09-03 21:25:28.313 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293150028508: 5958.888 at 00:33:38
2025-09-03 21:25:30.274 -03:00 [INF] [DEBUG] entry_tick for 293150028508: epoch=1756946018 -> 00:33:38.000, price=5958.888
2025-09-03 21:25:30.274 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293150028508: 5958.888 at 00:33:38
2025-09-03 21:25:30.274 -03:00 [INF] [TIMING] FALLBACK - Contrato 293150028508 detectado como finalizado
2025-09-03 21:25:30.274 -03:00 [INF] Contract 293150028508 finished: Profit=-1, Win=False
2025-09-03 21:25:30.275 -03:00 [INF] [DEBUG] exit_time for 293150028508: sell_time_epoch=1756946020 -> 00:33:40.000
2025-09-03 21:25:30.275 -03:00 [INF] [TIMING] Disparando ContractResult event às 21:25:30.275
2025-09-03 21:25:30.275 -03:00 [INF] [DEBUG] Martingale not enabled, ignoring contract result
2025-09-03 21:25:30.277 -03:00 [INF] Profit Table updated for contract 293150028508: Profit=-1, ExitPrice=5958.896, ExitTime=00:33:40
2025-09-03 21:25:30.277 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293150028508: 5958.888 at 00:33:38
2025-09-03 21:25:30.277 -03:00 [INF] [TIMING] ContractResult event concluído às 21:25:30.277 (duração: 2.4768ms)
2025-09-03 21:25:46.302 -03:00 [INF] Application is shutting down...
2025-09-03 21:34:32.608 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-03 21:34:32.631 -03:00 [INF] Hosting environment: Production
2025-09-03 21:34:32.631 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-09-03 21:34:32.881 -03:00 [INF] Conectando à API Deriv...
2025-09-03 21:34:33.658 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-03 21:34:34.085 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 21:34:34.087 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 21:34:34.104 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-03 21:34:34.104 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-03 21:34:34.621 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 21:34:34.621 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 21:34:34.622 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 21:34:34.622 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 21:34:34.622 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 21:34:34.622 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 21:34:34.622 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 21:34:34.622 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 21:34:34.622 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 21:34:34.625 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-03 21:34:34.756 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-03 21:35:29.233 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:29.233 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 21:35:29.244 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-03 21:35:30.768 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:30.768 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=1.00
2025-09-03 21:35:32.764 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:32.764 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-03 21:35:33.025 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:33.025 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-03 21:35:33.320 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:33.320 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-03 21:35:35.751 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:35.751 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.38
2025-09-03 21:35:37.647 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:37.647 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.3
2025-09-03 21:35:38.363 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:38.363 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-03 21:35:43.560 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:35:43.560 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=PUTE, Symbol=R_10, Stake=0.35
2025-09-03 21:35:53.404 -03:00 [WRN] Contrato PUTE não possui oposto definido
2025-09-03 21:36:21.689 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:36:21.689 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=DIGITODD, Symbol=R_10, Stake=0.35
2025-09-03 21:36:21.691 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-09-03 21:36:21.691 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-03 21:36:22.489 -03:00 [ERR] Erro ao calcular proposta
System.TimeoutException: Requisição Fast Martingale expirou - latência alta detectada.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 355
   at Excalibur.Services.DerivApiService.GetProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 404
   at Excalibur.ViewModels.MainViewModel.CalculateProposalAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1286
2025-09-03 21:36:29.950 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:36:29.951 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-03 21:36:30.180 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:36:30.180 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-03 21:36:30.292 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:36:30.292 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=DIGITODD, Symbol=R_10, Stake=0.4
2025-09-03 21:36:34.892 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-03 21:36:34.892 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=DIGITODD, Symbol=R_10, Stake=0.40
2025-09-03 21:36:35.108 -03:00 [WRN] Contrato DIGITODD não possui oposto definido
2025-09-03 21:36:54.878 -03:00 [INF] Application is shutting down...
