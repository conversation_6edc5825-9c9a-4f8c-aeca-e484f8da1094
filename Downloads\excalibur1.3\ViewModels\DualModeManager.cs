using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace Excalibur.ViewModels
{
    /// <summary>
    /// Manages dual mode trading logic following SOLID principles.
    /// Single Responsibility: Handles dual contract execution, stake calculation, and session management.
    /// Open/Closed: Extensible for new contract types without modifying existing code.
    /// Liskov Substitution: Can be replaced with different dual mode implementations.
    /// Interface Segregation: Focused interface for dual mode operations.
    /// Dependency Inversion: Depends on abstractions (ILogger) not concretions.
    /// </summary>
    public class DualModeManager
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, string> _contractOpposites;
        private readonly Random _random;

        // Current state tracking
        public int CurrentLevel { get; private set; } = 1;
        public int CurrentSession { get; private set; } = 1;
        public string? LastLosingContract { get; private set; }
        public decimal LowerStake { get; private set; }
        public decimal HigherStake { get; private set; }

        public DualModeManager(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _random = new Random();
            _contractOpposites = InitializeContractOpposites();
        }

        /// <summary>
        /// Initializes the mapping of contract types to their opposites.
        /// Following the instruction requirements for dual entry support.
        /// </summary>
        private Dictionary<string, string> InitializeContractOpposites()
        {
            return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                // Rise/Fall pairs
                { "CALL", "PUT" },
                { "PUT", "CALL" },
                { "RISE", "FALL" },
                { "FALL", "RISE" },

                // Higher/Lower pairs
                { "HIGHER", "LOWER" },
                { "LOWER", "HIGHER" },

                // Touch/No Touch pairs
                { "TOUCH", "NOTOUCH" },
                { "NOTOUCH", "TOUCH" },

                // Digit pairs - Even/Odd
                { "DIGITEVEN", "DIGITODD" },
                { "DIGITODD", "DIGITEVEN" },

                // Digit pairs - Over/Under (these require digit selection)
                { "DIGITOVER", "DIGITUNDER" },
                { "DIGITUNDER", "DIGITOVER" },

                // Digit pairs - Matches/Differs (these require digit selection)
                { "DIGITMATCH", "DIGITDIFF" },
                { "DIGITDIFF", "DIGITMATCH" },

                // Range pairs - Stays In/Goes Out
                { "EXPIRYRANGE", "EXPIRYMISS" },
                { "EXPIRYMISS", "EXPIRYRANGE" },
                { "RANGE", "UPORDOWN" },
                { "UPORDOWN", "RANGE" },

                // Asian pairs
                { "ASIANU", "ASIAND" },
                { "ASIAND", "ASIANU" },

                // Lookback pairs
                { "LBHIGHLOW", "LBFLOATCALL" },
                { "LBFLOATCALL", "LBHIGHLOW" },
                { "LBFLOATPUT", "LBHIGHLOW" },
                { "LBHIGHLOW", "LBFLOATPUT" }
            };
        }

        /// <summary>
        /// Gets the opposite contract type for dual entry.
        /// Throws ArgumentException if the contract type doesn't support dual mode.
        /// </summary>
        public string GetOppositeContractType(string contractType)
        {
            if (string.IsNullOrEmpty(contractType))
                throw new ArgumentException("Contract type cannot be null or empty", nameof(contractType));

            if (_contractOpposites.TryGetValue(contractType, out string? opposite))
            {
                _logger.LogInformation($"[DUAL] Contract {contractType} -> Opposite: {opposite}");
                return opposite;
            }

            throw new ArgumentException($"Contract type '{contractType}' does not support dual mode", nameof(contractType));
        }

        /// <summary>
        /// Processes a trading result and calculates stakes for the next level.
        /// Implements the dual mode logic as specified in the requirements.
        /// </summary>
        public void ProcessResult(string contractType, bool isWin, decimal baseStake, decimal takeProfit, decimal payout)
        {
            if (string.IsNullOrEmpty(contractType))
                throw new ArgumentException("Contract type cannot be null or empty", nameof(contractType));

            if (baseStake <= 0)
                throw new ArgumentException("Base stake must be positive", nameof(baseStake));

            if (takeProfit <= 0)
                throw new ArgumentException("Take profit must be positive", nameof(takeProfit));

            if (payout <= 0)
                throw new ArgumentException("Payout must be positive", nameof(payout));

            _logger.LogInformation($"[DUAL] Processing result - Contract: {contractType}, Win: {isWin}, Level: {CurrentLevel}");

            if (CurrentLevel == 1)
            {
                // First level: Calculate initial stakes
                CalculateInitialStakes(baseStake, takeProfit, payout);
            }
            else
            {
                // Subsequent levels: Track losing contract and maintain stakes
                if (!isWin)
                {
                    LastLosingContract = contractType;
                    _logger.LogInformation($"[DUAL] Level {CurrentLevel}: {contractType} lost, will receive higher stake next round");
                }
            }

            CurrentLevel++;
        }

        /// <summary>
        /// Calculates the initial stakes for the first dual entry.
        /// One stake is slightly higher to achieve the specified take profit.
        /// The choice of which stake is higher is random as per requirements.
        /// </summary>
        private void CalculateInitialStakes(decimal baseStake, decimal takeProfit, decimal payout)
        {
            _logger.LogInformation($"[DUAL] Calculating initial stakes - Base: {baseStake:F2}, Take Profit: {takeProfit:F2}, Payout: {payout:F2}");

            // The lower stake is the base stake
            LowerStake = baseStake;

            // Calculate the higher stake needed to achieve take profit
            // Formula: Higher stake must compensate for the loss of the lower stake and achieve take profit
            // When higher stake wins: (HigherStake * Payout) - LowerStake = TakeProfit
            // Therefore: HigherStake = (TakeProfit + LowerStake) / Payout
            HigherStake = Math.Round((takeProfit + LowerStake) / payout, 2);

            // Ensure minimum difference between stakes
            if (HigherStake - LowerStake < 0.01m)
            {
                HigherStake = LowerStake + 0.01m;
            }

            _logger.LogInformation($"[DUAL] Calculated stakes - Lower: {LowerStake:F2}, Higher: {HigherStake:F2}");
        }

        /// <summary>
        /// Resets the session state after completing all levels.
        /// Maintains session count and resets level tracking.
        /// </summary>
        public void ResetSession()
        {
            _logger.LogInformation($"[DUAL] Resetting session {CurrentSession}");

            CurrentLevel = 1;
            LastLosingContract = null;
            CurrentSession++;

            _logger.LogInformation($"[DUAL] Session reset complete. Now on session {CurrentSession}");
        }

        /// <summary>
        /// Resets all dual mode state for a fresh start.
        /// Used when dual mode is disabled or restarted.
        /// </summary>
        public void Reset()
        {
            _logger.LogInformation("[DUAL] Full reset initiated");

            CurrentLevel = 1;
            CurrentSession = 1;
            LastLosingContract = null;
            LowerStake = 0;
            HigherStake = 0;

            _logger.LogInformation("[DUAL] Full reset complete");
        }

        /// <summary>
        /// Determines if the contract type supports dual mode trading.
        /// </summary>
        public bool SupportsContractType(string contractType)
        {
            return !string.IsNullOrEmpty(contractType) && _contractOpposites.ContainsKey(contractType);
        }

        /// <summary>
        /// Gets the stake amount for a specific contract type in the current level.
        /// Implements the logic where higher stake goes to the losing contract from previous level.
        /// </summary>
        public decimal GetStakeForContract(string contractType, bool isFirstLevel)
        {
            if (string.IsNullOrEmpty(contractType))
                throw new ArgumentException("Contract type cannot be null or empty", nameof(contractType));

            if (isFirstLevel)
            {
                // First level: Random assignment of higher stake
                bool giveHigherStake = _random.Next(2) == 0;
                decimal stake = giveHigherStake ? HigherStake : LowerStake;

                _logger.LogInformation($"[DUAL] First level stake for {contractType}: {stake:F2} (Random: {giveHigherStake})");
                return stake;
            }
            else
            {
                // Subsequent levels: Higher stake goes to last losing contract
                bool shouldGetHigherStake = string.Equals(contractType, LastLosingContract, StringComparison.OrdinalIgnoreCase);
                decimal stake = shouldGetHigherStake ? HigherStake : LowerStake;

                _logger.LogInformation($"[DUAL] Level {CurrentLevel} stake for {contractType}: {stake:F2} (Last loser: {LastLosingContract})");
                return stake;
            }
        }

        /// <summary>
        /// Validates if the current configuration is valid for dual mode trading.
        /// </summary>
        public bool IsValidConfiguration(decimal takeProfit, int level, int session)
        {
            bool isValid = takeProfit > 0 && level > 0 && session > 0;

            if (!isValid)
            {
                _logger.LogWarning($"[DUAL] Invalid configuration - Take Profit: {takeProfit}, Level: {level}, Session: {session}");
            }

            return isValid;
        }
    }
}